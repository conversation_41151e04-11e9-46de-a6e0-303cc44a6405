/**
 * 
 */
package com.ailk.newchnl.service;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.ailk.newchnl.dao.*;
import com.ailk.newchnl.entity.*;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


import com.ailk.newchnl.entity.crm.CRM2Channel;
import com.ailk.newchnl.entity.flow.AgentAdjustFeeTemDtl;
import com.ailk.newchnl.entity.flow.AgentAdjustFlowUseDtl;
import com.ailk.newchnl.mybatis.pagination.PageData;
import com.ailk.newchnl.mybatis.pagination.PageParameter;
import com.ailk.newchnl.service.crm.CRM2ChannelService;
import com.ailk.newchnl.util.DateUtil;
import scala.Int;

/**
 * <AUTHOR> 2015-12
 *
 */
@Service("agentAdjustUseService")
public class AgentAdjustUseServiceImpl implements AgentAdjustUseService {
	private static final Logger logger = org.slf4j.LoggerFactory.getLogger(AgentAdjustUseServiceImpl.class);

	@Resource
	private AgentAdjustUseDao agentAdjustUseDao;

	@Resource
	private AgentAdjustFlowUseDtlDao agentAdjustFlowUseDtlDao;

	@Resource
	private AgentAdjustFeeTemDtlDao  agentAdjustFeeTemDtlDao;

	@Resource
	private ChannelNodeScoreDao channelNodeScoreDao;

	@Resource
	private ChannelNodeStandScoreDao channelNodeStandScoreDao;

	@Resource
	private CRM2ChannelService crm2ChannelService;

	@Autowired
	private TaskService taskService;


	@Resource
	private ChannelNotifyService channelNotifyService;


	@Override
	@Transactional(rollbackFor = {Exception.class},propagation = Propagation.REQUIRES_NEW)
	public String operatorAgentAdjustUse(Integer opeatorType,
			AgentAdjustUse agentAdjustUse) throws Exception {
		String mesager = "success";
		try {

			if(opeatorType == 0 || opeatorType == 1){
				Long agentAdjustUseId = agentAdjustUseDao.getSequence();
				/***
				 * 校验从账户是否正确
				 * 调用外部接口，根据登录名查询对应的数据
				 */
				CRM2Channel operatorStaff = crm2ChannelService.getCRM2ChannelOperatorCode(agentAdjustUse.getAgentAdjustCAccount());
				if(operatorStaff == null){
					mesager = "operError";
					return mesager;
				}if(operatorStaff.getCode() != null && operatorStaff.getCode().equals("error")){
					mesager = "operTimeOutError";
					return mesager;
				}
				//新增
				if(opeatorType == 0){
					agentAdjustUse.setAgentAdjustUseId(agentAdjustUseId);
					agentAdjustUse.setCreateDate(DateUtil.getCurrDate());
					agentAdjustUse.setRecStatus(1);
					agentAdjustUseDao.insert(agentAdjustUse);
				}if(opeatorType == 1){
					//先查询出来数据，将原数据无效，新加数据
					AgentAdjustUse adjustUse = new AgentAdjustUse();
					adjustUse.setAgentAdjustUseId(agentAdjustUse.getAgentAdjustUseId());
					List<AgentAdjustUse> adjustUses = agentAdjustUseDao.query(adjustUse);
					if(adjustUses.size() > 0 ){
						AgentAdjustUse adjustUse2 = adjustUses.get(0);
						adjustUse2.setRecStatus(0);
						agentAdjustUseDao.update(adjustUse2);
					}
					agentAdjustUse.setAgentAdjustUseId(agentAdjustUseId);
					agentAdjustUse.setCreateDate(adjustUses.get(0).getCreateDate());
					agentAdjustUse.setUpdateDate(DateUtil.getCurrDate());
					agentAdjustUse.setRecStatus(1);
					agentAdjustUseDao.insert(agentAdjustUse);
				}
			}	
			if(opeatorType == 2){
				agentAdjustUse.setRecStatus(0);
				agentAdjustUseDao.update(agentAdjustUse);
			}
		} catch (Exception e) {
			logger.error("审批人员维护管理出现异常",e);
			mesager = "error";
		}
		return mesager;
	}

	@Override
	public PageData<AgentAdjustUse> pageAgentAdjustUse(
			AgentAdjustUse adjustUse, PageParameter page) throws Exception {
		List<AgentAdjustUse> agentAdjustUses = new ArrayList<AgentAdjustUse>();
		try {
			agentAdjustUses = agentAdjustUseDao.query(adjustUse,page);
		} catch (Exception e) {
			logger.error("审批人员维护查询失败",e);
		}
		return new PageData<AgentAdjustUse>(agentAdjustUses, page);
	}

	@Override
	public List<AgentAdjustUse> queryAgentAdjustUse(
			AgentAdjustUse adjustUse) throws Exception {
		List<AgentAdjustUse> agentAdjustUses = new ArrayList<AgentAdjustUse>();
		try {
			agentAdjustUses = agentAdjustUseDao.query(adjustUse);
		} catch (Exception e) {
			logger.error("审批人员维护查询失败",e);
		}
		return agentAdjustUses;
	}

	@Override
	@Transactional(rollbackFor = {Exception.class},propagation = Propagation.REQUIRES_NEW)
	public Map<String, String> saveAgentAdjustUse(AgentAdjustUse adjustUse,
			AgentAdjustFeeTemDtl agentAdjustFeeTemDtl) throws Exception {

		return null;
	}

	@Override
	public OptionJson<ComboOption> getResCodeCombo(Integer resType) {
		List<ComboOption> resultSet = null;
		try{
			resultSet = agentAdjustUseDao.queryResType(resType);
		}catch(Exception e){

			logger.info("初始化资出错！");
		}
		return new OptionJson<ComboOption>(resultSet);
	}

	@Override
	public OptionJson<ComboOption> getResCodeComboByOrgId(Integer resType, SPrivData sPrivData) throws Exception{
		List<ComboOption> resultSet = null;
		try{
			resultSet = agentAdjustUseDao.queryResTypeByOrgId(resType, sPrivData.getOrgId());
		}catch(Exception e) {
			logger.info("初始化资出错！");
		}
		return new OptionJson<ComboOption>(resultSet);
	}

	@Override
	public PageData<AgentAdjustFlowUseDtl> pageAgentAdjustFlowUseDtl(
			Long flowType, String flowUseName, Long orderStatus,String useName, PageParameter page) throws Exception {
		List<AgentAdjustFlowUseDtl> agentAdjustFlowUseDtls = new ArrayList<AgentAdjustFlowUseDtl>();
		try {
			AgentAdjustFlowUseDtl adjustFlowUseDtl = new AgentAdjustFlowUseDtl();
			adjustFlowUseDtl.setFlowJudgeType(flowType);
			adjustFlowUseDtl.setFlowUseName(flowUseName);
			if(orderStatus == -1L){
				adjustFlowUseDtl.setOrderStatus(-1L);
			}else{
				adjustFlowUseDtl.setErrOrderStatus(1L);
			}
			adjustFlowUseDtl.setUseName(useName);
			agentAdjustFlowUseDtls = agentAdjustFlowUseDtlDao.query(adjustFlowUseDtl);
		} catch (Exception e) {
			logger.error("查询流程数据失败",e);
		}
		return new PageData<AgentAdjustFlowUseDtl>(agentAdjustFlowUseDtls, page);
	}

	@Override
	public PageData<AgentAdjustFeeTemDtl> pageAgentAdjustFeeTemDtl(Long batchId,PageParameter page)
			throws Exception {
		List<AgentAdjustFeeTemDtl> agentAdjustFeeTemDtls = new ArrayList<AgentAdjustFeeTemDtl>();
		List<AgentAdjustFeeTemDtl> agentAdjustFeeTemDtls1 = new ArrayList<AgentAdjustFeeTemDtl>();
		try {
			AgentAdjustFeeTemDtl adjustFeeTemDtl = new AgentAdjustFeeTemDtl();
			adjustFeeTemDtl.setBatchId(batchId);
			adjustFeeTemDtl.setResStatus(-1L);
			agentAdjustFeeTemDtls = agentAdjustFeeTemDtlDao.query(adjustFeeTemDtl,page);
			/**
			 * 金额转换
			 */
			if(agentAdjustFeeTemDtls.size() > 0 ){
				for(int i =0; i < agentAdjustFeeTemDtls.size(); i++){
					AgentAdjustFeeTemDtl agentAdjustFeeTemDtl = agentAdjustFeeTemDtls.get(i);
					String defualFee = agentAdjustFeeTemDtl.getAmount().toString();
					try {
						if(defualFee.equals("0")){
							agentAdjustFeeTemDtl.setAmountStr("0.00");
						}else{
							double a = new Double(defualFee);
							a = a /100;
							DecimalFormat df = new DecimalFormat();
							df.setMaximumFractionDigits(2);  
							df.setMinimumFractionDigits(2);
							df.setGroupingUsed(false);
							defualFee = df.format(a);  //将数据格式化保存double类型
							agentAdjustFeeTemDtl.setAmountStr(defualFee);
						}
					}catch(Exception e){
						logger.error("将酬金合作方分转元失败", e);
					}
					agentAdjustFeeTemDtls1.add(agentAdjustFeeTemDtl);
				}
			}
		} catch (Exception e) {
			logger.error("根据batchOId查询合作方酬金录入数据失败");
		}
		return new PageData<AgentAdjustFeeTemDtl>(agentAdjustFeeTemDtls,page);
	}
	/***
	 * 内部审批流程
	 */
	@Override
	@Transactional(rollbackFor={Exception.class},propagation = Propagation.REQUIRES_NEW)
	public String interiorbuildJudege(
			AgentAdjustFlowUseDtl adjustFlowUseDtl, Integer operaType)
					throws Exception {
		String messager = "";
		List<Task> tasks = taskService.createTaskQuery().processVariableValueEquals("batchId", adjustFlowUseDtl.getBatchId()).list();
		try {
			Task task = tasks.get(0);
			logger.info("------------"+taskService.getVariables(task.getId()).get("isInterior"));
			Long opId = (Long)taskService.getVariables(task.getId()).get("opId");
			Long orgId = (Long)taskService.getVariables(task.getId()).get("orgId");

			//审批通过 起草人审批数据通过 内部流程（2）	市场部审批流程
			if(operaType == 0){
				/**
				 * 查询选择的审批人信息
				 */
				AgentAdjustUse agentAdjustUse = new AgentAdjustUse();
				if(StringUtils.isNumeric(adjustFlowUseDtl.getFlowUseName().trim())){
					agentAdjustUse.setAgentAdjustUseId(Long.valueOf(adjustFlowUseDtl.getFlowUseName()));
					agentAdjustUse.setAgentAdjustName(null);
				}else{
					agentAdjustUse.setAgentAdjustName(adjustFlowUseDtl.getFlowUseName());
				}
				List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(agentAdjustUse);
				if(agentAdjustUses.size() <= 0){
					messager = "error2";
					return messager;
				}
				taskService.setVariable(task.getId(), "isInterior", true);
				AgentAdjustFlowUseDtl adjustFlowUseDtl3 = new AgentAdjustFlowUseDtl();
				adjustFlowUseDtl3.setFlowId(adjustFlowUseDtl.getFlowId());
				adjustFlowUseDtl3.setBatchId(adjustFlowUseDtl.getBatchId());
				adjustFlowUseDtl3.setUseName(adjustFlowUseDtl.getUseName());
				List<AgentAdjustFlowUseDtl> agentAdjustFlowUseDtls = agentAdjustFlowUseDtlDao.query(adjustFlowUseDtl3);
				if(agentAdjustFlowUseDtls.size() > 0){
					/**
					 * 将起草人数据置为无效，新增数据
					 */
					AgentAdjustFlowUseDtl adjustFlowUseDtl4 = agentAdjustFlowUseDtls.get(0);
					adjustFlowUseDtl4.setRecStatus(0L);
					if(adjustFlowUseDtl4.getFlowType() == 2L){
						adjustFlowUseDtl4.setExt1("三级经理审批");
						adjustFlowUseDtl4.setExt2("三级经理审批");	
					}if(adjustFlowUseDtl4.getFlowType() == 3L){
						adjustFlowUseDtl4.setExt1("二级经理审批");
						adjustFlowUseDtl4.setExt2("二级经理审批");	
					}
					agentAdjustFlowUseDtlDao.update(adjustFlowUseDtl4);
					boolean judge = false;
					if(adjustFlowUseDtl4.getFlowType() == 2 && adjustFlowUseDtl4.getFlowJudgeType() ==1){
						judge = true;
						adjustFlowUseDtl4.setOrderStatus(adjustFlowUseDtl.getFlowType());
						adjustFlowUseDtl4.setFlowType(adjustFlowUseDtl.getFlowType());
						adjustFlowUseDtl4.setRecStatus(1L);
						if(adjustFlowUseDtl4.getFlowType() == 2L){
							adjustFlowUseDtl4.setExt1("三级经理审批");
							adjustFlowUseDtl4.setExt2("三级经理审批");	
						}if(adjustFlowUseDtl4.getFlowType() == 3L){
							adjustFlowUseDtl4.setExt1("二级经理审批");
							adjustFlowUseDtl4.setExt2("二级经理审批");	
						}
						adjustFlowUseDtl4.setFlowUseName(agentAdjustUses.get(0).getAgentAdjustName());
						adjustFlowUseDtl4.setFlowUseId(agentAdjustUses.get(0).getAgentAdjustUseId());
						adjustFlowUseDtl4.setFlowUsePhone(agentAdjustUses.get(0).getAgentAdjustPhone());
						adjustFlowUseDtl4.setUseName(agentAdjustUses.get(0).getAgentAdjustCAccount());
						adjustFlowUseDtl4.setOpId(opId);
						adjustFlowUseDtl4.setOrgId(orgId);
						agentAdjustFlowUseDtlDao.insert(adjustFlowUseDtl4);
					}
					String messager1 = "";
					if(judge){
						messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为'"+adjustFlowUseDtl.getFlowName()+"'";	
					}else{
						messager1 = "提示：您的渠道管理系统有一张工单审批完成，工单名称为'"+adjustFlowUseDtl.getFlowName()+"'";
					}
					/***
					 * 根据选择的经理级别获取手机号码
					 */
					logger.info(messager1);
					String smsCode = "************";
					SPrivData sPrivData1 = new SPrivData();
					sPrivData1.setOpId(999990131L);
					sPrivData1.setOrgId(0L);
					channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);
				}else{
					messager = "error1";
					return messager;
				}
			}
			//审批驳回
			if(operaType == 1){
				taskService.setVariable(task.getId(), "isInterior", false);
				AgentAdjustFlowUseDtl adjustFlowUseDtl3 = new AgentAdjustFlowUseDtl();
				adjustFlowUseDtl3.setFlowId(adjustFlowUseDtl.getFlowId());
				adjustFlowUseDtl3.setBatchId(adjustFlowUseDtl.getBatchId());
				adjustFlowUseDtl3.setUseName(adjustFlowUseDtl.getUseName());
//				adjustFlowUseDtl3.setOrderStatus(1L);
				List<AgentAdjustFlowUseDtl> agentAdjustFlowUseDtls = agentAdjustFlowUseDtlDao.query(adjustFlowUseDtl3);

				AgentAdjustFlowUseDtl adjustFlowUseDtl2 = agentAdjustFlowUseDtls.get(0);
				adjustFlowUseDtl2.setOrderStatus(-1L); 
				adjustFlowUseDtl2.setOpId(opId);
				adjustFlowUseDtl2.setOrgId(orgId);
				adjustFlowUseDtl2.setBatchId(adjustFlowUseDtl.getBatchId());
				adjustFlowUseDtl2.setFlowId(adjustFlowUseDtl.getFlowId());
				adjustFlowUseDtl2.setExt1("审批被驳回");
				adjustFlowUseDtl2.setExt2("审批被驳回");
				adjustFlowUseDtl2.setExt4(adjustFlowUseDtl.getExt4());
				agentAdjustFlowUseDtlDao.update(adjustFlowUseDtl2);

				/***
				 * 根据选择的经理级别获取手机号码
				 */

				String messager1 = "提示：您的渠道管理系统有一张审批工单被驳回，工单名称为'"+adjustFlowUseDtl.getFlowName()+"'，驳回理由为：" + adjustFlowUseDtl.getExt4();
				logger.info(messager1);
				String smsCode = "************";
				SPrivData sPrivData1 = new SPrivData();
				sPrivData1.setOpId(999990131L);
				sPrivData1.setOrgId(0L);
				channelNotifyService.sendSMSMessage(smsCode, String.valueOf(adjustFlowUseDtl2.getFlowUsePhone()), messager1, DateUtil.getCurrDate(), sPrivData1);
			}if(operaType == 2){  //业务主管
				/**
				 * 查询选择的审批人信息
				 */
				AgentAdjustUse agentAdjustUse = new AgentAdjustUse();
				if(StringUtils.isNumeric(adjustFlowUseDtl.getFlowUseName().trim())){
					agentAdjustUse.setAgentAdjustUseId(Long.valueOf(adjustFlowUseDtl.getFlowUseName()));
					agentAdjustUse.setAgentAdjustName(null);
				}else{
					agentAdjustUse.setAgentAdjustName(adjustFlowUseDtl.getFlowUseName());
				}
				List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(agentAdjustUse);
				if(agentAdjustUses.size() <= 0){
					messager = "error2";
					return messager;
				}
				taskService.setVariable(task.getId(), "isInterior", true);
				AgentAdjustFlowUseDtl adjustFlowUseDtl3 = new AgentAdjustFlowUseDtl();
				adjustFlowUseDtl3.setFlowId(adjustFlowUseDtl.getFlowId());
				adjustFlowUseDtl3.setBatchId(adjustFlowUseDtl.getBatchId());
				adjustFlowUseDtl3.setUseName(adjustFlowUseDtl.getUseName());
				List<AgentAdjustFlowUseDtl> agentAdjustFlowUseDtls = agentAdjustFlowUseDtlDao.query(adjustFlowUseDtl3);
				if(agentAdjustFlowUseDtls.size() > 0){
					AgentAdjustFlowUseDtl adjustFlowUseDtl4 = agentAdjustFlowUseDtls.get(0);
					adjustFlowUseDtl4.setRecStatus(0L);
					if(adjustFlowUseDtl4.getFlowType() == 100L){
						adjustFlowUseDtl4.setExt1("市场部业务主管审批");
						adjustFlowUseDtl4.setExt2("市场部业务主管审批");
					}/*if(adjustFlowUseDtl4.getFlowType() == 101L){
						adjustFlowUseDtl4.setExt1("业务主管三级经理审批");
						adjustFlowUseDtl4.setExt2("业务主管三级经理审批");
					}if(adjustFlowUseDtl4.getFlowType() == 102L){
						adjustFlowUseDtl4.setExt1("业务主管二级经理审批");
						adjustFlowUseDtl4.setExt2("业务主管二级经理审批");
					}*/
					agentAdjustFlowUseDtlDao.update(adjustFlowUseDtl4);
					/*if(!adjustFlowUseDtl4.getExt2().equals("业务主管二级经理审批") && adjustFlowUseDtl4.getRecStatus()!= 1L){
						adjustFlowUseDtl4.setRecStatus(1L);
						adjustFlowUseDtl4.setOrderStatus(adjustFlowUseDtl.getFlowType());
						adjustFlowUseDtl4.setFlowType(adjustFlowUseDtl.getFlowType());
						if(adjustFlowUseDtl4.getFlowType() == 101L){
							adjustFlowUseDtl4.setExt1("业务主管三级经理审批");
							adjustFlowUseDtl4.setExt2("业务主管三级经理审批");	
						}if(adjustFlowUseDtl4.getFlowType() == 102L){
							adjustFlowUseDtl4.setExt1("业务主管二级经理审批");	
							adjustFlowUseDtl4.setExt2("业务主管二级经理审批");	
						}
						adjustFlowUseDtl4.setOpId(opId);
						adjustFlowUseDtl4.setOrgId(orgId);
						
						adjustFlowUseDtl4.setFlowUseName(agentAdjustUses.get(0).getAgentAdjustName());
						adjustFlowUseDtl4.setFlowUseId(agentAdjustUses.get(0).getAgentAdjustUseId());
						adjustFlowUseDtl4.setFlowUsePhone(agentAdjustUses.get(0).getAgentAdjustPhone());
						adjustFlowUseDtl4.setUseName(agentAdjustUses.get(0).getAgentAdjustCAccount());
						agentAdjustFlowUseDtlDao.insert(adjustFlowUseDtl4);
					}*/
					/***
					 * 根据选择的经理级别获取手机号码
					 */

					String messager1 = "提示：您的渠道管理系统有一张审批工单，工单名称为'"+adjustFlowUseDtl.getFlowName()+"'";
					logger.info(messager1);
					String smsCode = "************";
					SPrivData sPrivData1 = new SPrivData();
					sPrivData1.setOpId(999990131L);
					sPrivData1.setOrgId(0L);
					channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);
				}else{
					messager = "error1";
					return messager;
				}
			}
			if(operaType == 3){
				/**
				 * 查询选择的审批人信息
				 */
				AgentAdjustUse agentAdjustUse = new AgentAdjustUse();
				if(StringUtils.isNumeric(adjustFlowUseDtl.getFlowUseName().trim())){
					agentAdjustUse.setAgentAdjustUseId(Long.valueOf(adjustFlowUseDtl.getFlowUseName()));
					agentAdjustUse.setAgentAdjustName(null);
				}else{
					agentAdjustUse.setAgentAdjustName(adjustFlowUseDtl.getFlowUseName());
				}
				List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(agentAdjustUse);
				if(agentAdjustUses.size() <= 0){
					messager = "error2";
					return messager;
				}
				taskService.setVariable(task.getId(), "isInterior", true);
				AgentAdjustFlowUseDtl adjustFlowUseDtl3 = new AgentAdjustFlowUseDtl();
				adjustFlowUseDtl3.setFlowId(adjustFlowUseDtl.getFlowId());
				adjustFlowUseDtl3.setBatchId(adjustFlowUseDtl.getBatchId());
				adjustFlowUseDtl3.setUseName(adjustFlowUseDtl.getUseName());
				List<AgentAdjustFlowUseDtl> agentAdjustFlowUseDtls = agentAdjustFlowUseDtlDao.query(adjustFlowUseDtl3);
				if(agentAdjustFlowUseDtls.size() > 0){
					/**
					 * 将起草人数据置为无效，新增数据
					 */
					AgentAdjustFlowUseDtl adjustFlowUseDtl4 = agentAdjustFlowUseDtls.get(0);
					adjustFlowUseDtl4.setRecStatus(0L);
					if(adjustFlowUseDtl4.getFlowType() == 2L){
						adjustFlowUseDtl4.setExt1("三级经理审批");
						adjustFlowUseDtl4.setExt2("三级经理审批");	
					}if(adjustFlowUseDtl4.getFlowType() == 3L){
						adjustFlowUseDtl4.setExt1("二级经理审批");
						adjustFlowUseDtl4.setExt2("二级经理审批");	
					}if(adjustFlowUseDtl4.getFlowType() == 100L){
						adjustFlowUseDtl4.setExt1("市场部业务主管审批");
						adjustFlowUseDtl4.setExt2("市场部业务主管审批");
					}/*if(adjustFlowUseDtl4.getFlowType() == 101L){
						adjustFlowUseDtl4.setExt2("市场部业务主管审批");
						adjustFlowUseDtl4.setExt1("业务主管三级经理审批");
						adjustFlowUseDtl4.setExt2("业务主管三级经理审批");
					}if(adjustFlowUseDtl4.getFlowType() == 102L){
						adjustFlowUseDtl4.setExt1("业务主管二级经理审批");
						adjustFlowUseDtl4.setExt2("业务主管二级经理审批");
					}*/
					agentAdjustFlowUseDtlDao.update(adjustFlowUseDtl4);
					boolean judge = true;
					if(adjustFlowUseDtl.getFlowType() !=102L){
						judge = false;
						adjustFlowUseDtl4.setRecStatus(1L);
						adjustFlowUseDtl4.setOrderStatus(adjustFlowUseDtl.getFlowType());
						adjustFlowUseDtl4.setFlowType(adjustFlowUseDtl.getFlowType());
						if(adjustFlowUseDtl4.getFlowType() == 3L){
							adjustFlowUseDtl4.setExt1("二级经理审批");
							adjustFlowUseDtl4.setExt2("二级经理待审批");
						}
						/*if(adjustFlowUseDtl4.getFlowType() == 101L){
							adjustFlowUseDtl4.setExt1("业务主管三级经理审批");
							adjustFlowUseDtl4.setExt2("业务主管三级经理审批");	
						}if(adjustFlowUseDtl4.getFlowType() == 102L){
							adjustFlowUseDtl4.setExt1("业务主管二级经理审批");	
							adjustFlowUseDtl4.setExt2("业务主管二级经理审批");	
						}*/
						adjustFlowUseDtl4.setOpId(opId);
						adjustFlowUseDtl4.setOrgId(orgId);
						
						adjustFlowUseDtl4.setFlowUseName(agentAdjustUses.get(0).getAgentAdjustName());
						adjustFlowUseDtl4.setFlowUseId(agentAdjustUses.get(0).getAgentAdjustUseId());
						adjustFlowUseDtl4.setFlowUsePhone(agentAdjustUses.get(0).getAgentAdjustPhone());
						adjustFlowUseDtl4.setUseName(agentAdjustUses.get(0).getAgentAdjustCAccount());
						agentAdjustFlowUseDtlDao.insert(adjustFlowUseDtl4);
					}
					String messager1 = "";
					if(judge == false ){
						messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为'"+adjustFlowUseDtl.getFlowName()+"'";
					}else{
						messager1 = "提示：您的渠道管理系统有一张审批完成，工单名称为'"+adjustFlowUseDtl.getFlowName()+"'";
					}
					/***
					 * 根据选择的经理级别获取手机号码
					 */

					
					logger.info(messager1);
					String smsCode = "************";
					SPrivData sPrivData1 = new SPrivData();
					sPrivData1.setOpId(999990131L);
					sPrivData1.setOrgId(0L);
					channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);
				}else{
					messager = "error1";
					return messager;
				}
			}
			if(operaType == 4){
				/**
				 * 查询选择的审批人信息
				 */
				AgentAdjustUse agentAdjustUse = new AgentAdjustUse();
				if(StringUtils.isNumeric(adjustFlowUseDtl.getFlowUseName().trim())){
					agentAdjustUse.setAgentAdjustUseId(Long.valueOf(adjustFlowUseDtl.getFlowUseName()));
					agentAdjustUse.setAgentAdjustName(null);
				}else{
					agentAdjustUse.setAgentAdjustName(adjustFlowUseDtl.getFlowUseName());
				}
				List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(agentAdjustUse);
				if(agentAdjustUses.size() <= 0){
					messager = "error2";
					return messager;
				}
				taskService.setVariable(task.getId(), "isInterior", true);
				AgentAdjustFlowUseDtl adjustFlowUseDtl3 = new AgentAdjustFlowUseDtl();
				adjustFlowUseDtl3.setFlowId(adjustFlowUseDtl.getFlowId());
				adjustFlowUseDtl3.setBatchId(adjustFlowUseDtl.getBatchId());
				adjustFlowUseDtl3.setUseName(adjustFlowUseDtl.getUseName());
				List<AgentAdjustFlowUseDtl> agentAdjustFlowUseDtls = agentAdjustFlowUseDtlDao.query(adjustFlowUseDtl3);
				if(agentAdjustFlowUseDtls.size() > 0){
					AgentAdjustFlowUseDtl adjustFlowUseDtl4 = agentAdjustFlowUseDtls.get(0);
					adjustFlowUseDtl4.setRecStatus(0L);
					if(adjustFlowUseDtl4.getFlowType() == 3L){
						adjustFlowUseDtl4.setExt1("二级经理审批");
						adjustFlowUseDtl4.setExt2("二级经理审批");
					}if(adjustFlowUseDtl4.getFlowType() == 100L){
						adjustFlowUseDtl4.setExt1("市场部业务主管审批");
						adjustFlowUseDtl4.setExt2("市场部业务主管审批");
					}
					agentAdjustFlowUseDtlDao.update(adjustFlowUseDtl4);
					if(adjustFlowUseDtl4.getFlowType()==3l){
						adjustFlowUseDtl4.setRecStatus(1L);
						adjustFlowUseDtl4.setOrderStatus(adjustFlowUseDtl.getFlowType());
						adjustFlowUseDtl4.setFlowType(adjustFlowUseDtl.getFlowType());
						adjustFlowUseDtl4.setExt1("市场部业务主管审批");
						adjustFlowUseDtl4.setExt2("市场部业务主管待审批");
						adjustFlowUseDtl4.setOpId(opId);
						adjustFlowUseDtl4.setOrgId(orgId);

						adjustFlowUseDtl4.setFlowUseName(agentAdjustUses.get(0).getAgentAdjustName());
						adjustFlowUseDtl4.setFlowUseId(agentAdjustUses.get(0).getAgentAdjustUseId());
						adjustFlowUseDtl4.setFlowUsePhone(agentAdjustUses.get(0).getAgentAdjustPhone());
						adjustFlowUseDtl4.setUseName(agentAdjustUses.get(0).getAgentAdjustCAccount());
						agentAdjustFlowUseDtlDao.insert(adjustFlowUseDtl4);
					}
					/***
					 * 根据选择的经理级别获取手机号码
					 */

					String messager1 = "提示：您的渠道管理系统有一张审批通过，工单名称为'"+adjustFlowUseDtl.getFlowName()+"'";
					logger.info(messager1);
					String smsCode = "************";
					SPrivData sPrivData1 = new SPrivData();
					sPrivData1.setOpId(999990131L);
					sPrivData1.setOrgId(0L);
					channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);
				}else{
					messager = "error1";
					return messager;
				}
			}
			//业务主管二级经理审批
			if(operaType == 5){
				/**
				 * 查询选择的审批人信息
				 */
				AgentAdjustUse agentAdjustUse = new AgentAdjustUse();
				if(StringUtils.isNumeric(adjustFlowUseDtl.getFlowUseName().trim())){
					agentAdjustUse.setAgentAdjustUseId(Long.valueOf(adjustFlowUseDtl.getFlowUseName()));
					agentAdjustUse.setAgentAdjustName(null);
				}else{
					agentAdjustUse.setAgentAdjustName(adjustFlowUseDtl.getFlowUseName());
				}
				List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(agentAdjustUse);
				if(agentAdjustUses.size() <= 0){
					messager = "error2";
					return messager;
				}
				taskService.setVariable(task.getId(), "isInterior", true);
				AgentAdjustFlowUseDtl adjustFlowUseDtl1 = new AgentAdjustFlowUseDtl();
				adjustFlowUseDtl1.setFlowId(adjustFlowUseDtl.getFlowId());
				adjustFlowUseDtl1.setBatchId(adjustFlowUseDtl.getBatchId());
				adjustFlowUseDtl1.setOrderStatus(101L);
				List<AgentAdjustFlowUseDtl> agentAdjustFlowUseDtls = agentAdjustFlowUseDtlDao.query(adjustFlowUseDtl1);
				if(agentAdjustFlowUseDtls.size() > 0){
					AgentAdjustFlowUseDtl adjustFlowUseDtl4 = agentAdjustFlowUseDtls.get(0);
					adjustFlowUseDtl4.setRecStatus(0L);
					adjustFlowUseDtl4.setExt1("业务主管三级经理审批通过");
					agentAdjustFlowUseDtlDao.update(adjustFlowUseDtl4);

					adjustFlowUseDtl4.setRecStatus(1L);
					adjustFlowUseDtl4.setOrderStatus(102L);
					adjustFlowUseDtl4.setExt1("审批完成");
					adjustFlowUseDtl4.setExt2("审批完成");
					adjustFlowUseDtl4.setOpId(opId);
					adjustFlowUseDtl4.setOrgId(orgId);
					adjustFlowUseDtl4.setFlowUseName(agentAdjustUses.get(0).getAgentAdjustName());
					adjustFlowUseDtl4.setFlowUseId(agentAdjustUses.get(0).getAgentAdjustUseId());
					adjustFlowUseDtl4.setFlowUsePhone(agentAdjustUses.get(0).getAgentAdjustPhone());
					adjustFlowUseDtl4.setUseName(agentAdjustUses.get(0).getAgentAdjustCAccount());
					agentAdjustFlowUseDtlDao.insert(adjustFlowUseDtl4);

					/***
					 * 根据选择的经理级别获取手机号码
					 */
					String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为'"+adjustFlowUseDtl.getFlowName()+"'";
					logger.info(messager1);
					logger.info(messager1);
					String smsCode = "************";
					SPrivData sPrivData1 = new SPrivData();
					sPrivData1.setOpId(999990131L);
					sPrivData1.setOrgId(0L);
					channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);
				}else{
					messager = "error1";
					return messager;
				}
			}if(operaType == 6){  //二级经理审批通过
				/**
				 * 查询选择的审批人信息
				 */
				AgentAdjustUse agentAdjustUse = new AgentAdjustUse();
				if(StringUtils.isNumeric(adjustFlowUseDtl.getFlowUseName().trim())){
					agentAdjustUse.setAgentAdjustUseId(Long.valueOf(adjustFlowUseDtl.getFlowUseName()));
					agentAdjustUse.setAgentAdjustName(null);
				}else{
					agentAdjustUse.setAgentAdjustName(adjustFlowUseDtl.getFlowUseName());
				}
				List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(agentAdjustUse);
				if(agentAdjustUses.size() <= 0){
					messager = "error2";
					return messager;
				}
				taskService.setVariable(task.getId(), "isInterior", true);
				AgentAdjustFlowUseDtl adjustFlowUseDtl3 = new AgentAdjustFlowUseDtl();
				adjustFlowUseDtl3.setFlowId(adjustFlowUseDtl.getFlowId());
				adjustFlowUseDtl3.setBatchId(adjustFlowUseDtl.getBatchId());
				adjustFlowUseDtl3.setOrderStatus(2L);
				List<AgentAdjustFlowUseDtl> agentAdjustFlowUseDtls = agentAdjustFlowUseDtlDao.query(adjustFlowUseDtl3);
				if(agentAdjustFlowUseDtls.size() > 0){
					AgentAdjustFlowUseDtl adjustFlowUseDtl4 = agentAdjustFlowUseDtls.get(0);
					adjustFlowUseDtl4.setRecStatus(0L); //三级经理审批通过
					adjustFlowUseDtl4.setExt1("三级经理审批通过");
					agentAdjustFlowUseDtlDao.update(adjustFlowUseDtl4);

					adjustFlowUseDtl4.setRecStatus(1L);
					adjustFlowUseDtl4.setOrderStatus(3L);
					adjustFlowUseDtl4.setExt1("二级经理审批通过");
					adjustFlowUseDtl4.setOpId(opId);
					adjustFlowUseDtl4.setOrgId(orgId);
					adjustFlowUseDtl4.setFlowUseName(agentAdjustUses.get(0).getAgentAdjustName());
					adjustFlowUseDtl4.setFlowUseId(agentAdjustUses.get(0).getAgentAdjustUseId());
					adjustFlowUseDtl4.setFlowUsePhone(agentAdjustUses.get(0).getAgentAdjustPhone());
					adjustFlowUseDtl4.setUseName(agentAdjustUses.get(0).getAgentAdjustCAccount());
					agentAdjustFlowUseDtlDao.insert(adjustFlowUseDtl4);


					/***
					 * 根据选择的经理级别获取手机号码
					 */

					String messager1 = "提示：您的渠道管理系统有一张审批通过，工单名称为'"+adjustFlowUseDtl.getFlowName()+"'";
					logger.info(messager1);
					String smsCode = "************";
					SPrivData sPrivData1 = new SPrivData();
					sPrivData1.setOpId(999990131L);
					sPrivData1.setOrgId(0L);
					channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);
				}else{
					messager = "error1";
					return messager;
				}
			}if(operaType == 7){  //二级经理审批通过
				/**
				 * 查询选择的审批人信息
				 */
				AgentAdjustUse agentAdjustUse = new AgentAdjustUse();
				if(StringUtils.isNumeric(adjustFlowUseDtl.getFlowUseName().trim())){
					agentAdjustUse.setAgentAdjustUseId(Long.valueOf(adjustFlowUseDtl.getFlowUseName()));
					agentAdjustUse.setAgentAdjustName(null);
				}else{
					agentAdjustUse.setAgentAdjustName(adjustFlowUseDtl.getFlowUseName());
				}
				List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(agentAdjustUse);
				if(agentAdjustUses.size() <= 0){
					messager = "error2";
					return messager;
				}
				taskService.setVariable(task.getId(), "isInterior", true);
				AgentAdjustFlowUseDtl adjustFlowUseDtl3 = new AgentAdjustFlowUseDtl();
				adjustFlowUseDtl3.setFlowId(adjustFlowUseDtl.getFlowId());
				adjustFlowUseDtl3.setBatchId(adjustFlowUseDtl.getBatchId());
				adjustFlowUseDtl3.setOrderStatus(3L);
				List<AgentAdjustFlowUseDtl> agentAdjustFlowUseDtls = agentAdjustFlowUseDtlDao.query(adjustFlowUseDtl3);
				if(agentAdjustFlowUseDtls.size() > 0){
					AgentAdjustFlowUseDtl adjustFlowUseDtl4 = agentAdjustFlowUseDtls.get(0);
					adjustFlowUseDtl4.setRecStatus(0L); //三级经理审批通过
					adjustFlowUseDtl4.setExt1("二级经理审批通过");
					agentAdjustFlowUseDtlDao.update(adjustFlowUseDtl4);

					adjustFlowUseDtl4.setRecStatus(1L);
					adjustFlowUseDtl4.setOrderStatus(100L);
					adjustFlowUseDtl4.setExt1("市场部业务主管审批通过");
					adjustFlowUseDtl4.setOpId(opId);
					adjustFlowUseDtl4.setOrgId(orgId);
					adjustFlowUseDtl4.setFlowUseName(agentAdjustUses.get(0).getAgentAdjustName());
					adjustFlowUseDtl4.setFlowUseId(agentAdjustUses.get(0).getAgentAdjustUseId());
					adjustFlowUseDtl4.setFlowUsePhone(agentAdjustUses.get(0).getAgentAdjustPhone());
					adjustFlowUseDtl4.setUseName(agentAdjustUses.get(0).getAgentAdjustCAccount());
					agentAdjustFlowUseDtlDao.insert(adjustFlowUseDtl4);


					/***
					 * 根据选择的经理级别获取手机号码
					 */

					String messager1 = "提示：您的渠道管理系统有一张审批通过，工单名称为'"+adjustFlowUseDtl.getFlowName()+"'";
					logger.info(messager1);
					logger.info(messager1);
					String smsCode = "************";
					SPrivData sPrivData1 = new SPrivData();
					sPrivData1.setOpId(999990131L);
					sPrivData1.setOrgId(0L);
					channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);
				}else{
					messager = "error1";
					return messager;
				}
			}if(operaType == 10){
				/**
				 * 查询选择的审批人信息
				 */
				AgentAdjustUse agentAdjustUse = new AgentAdjustUse();
				if(StringUtils.isNumeric(adjustFlowUseDtl.getFlowUseName().trim())){
					agentAdjustUse.setAgentAdjustUseId(Long.valueOf(adjustFlowUseDtl.getFlowUseName()));
					agentAdjustUse.setAgentAdjustName(null);
				}else{
					agentAdjustUse.setAgentAdjustName(adjustFlowUseDtl.getFlowUseName());
				}
				List<AgentAdjustUse> agentAdjustUses = agentAdjustUseDao.query(agentAdjustUse);
				if(agentAdjustUses.size() <= 0){
					messager = "error2";
					return messager;
				}
				taskService.setVariable(task.getId(), "isInterior", true);
				AgentAdjustFlowUseDtl adjustFlowUseDtl1 = new AgentAdjustFlowUseDtl();
				adjustFlowUseDtl1.setFlowId(adjustFlowUseDtl.getFlowId());
				adjustFlowUseDtl1.setBatchId(adjustFlowUseDtl.getBatchId());
				adjustFlowUseDtl1.setOrderStatus(-1L);
				List<AgentAdjustFlowUseDtl> agentAdjustFlowUseDtls = agentAdjustFlowUseDtlDao.query(adjustFlowUseDtl1);
				if(agentAdjustFlowUseDtls.size() > 0){
					AgentAdjustFlowUseDtl adjustFlowUseDtl4 = agentAdjustFlowUseDtls.get(0);
					adjustFlowUseDtl4.setOrderStatus(1L);
					adjustFlowUseDtl4.setExt1("驳回审批通过");
					adjustFlowUseDtl4.setOpId(opId);
					adjustFlowUseDtl4.setOrgId(orgId);
					adjustFlowUseDtl4.setFlowUseName(agentAdjustUses.get(0).getAgentAdjustName());
					adjustFlowUseDtl4.setFlowUseId(agentAdjustUses.get(0).getAgentAdjustUseId());
					adjustFlowUseDtl4.setFlowUsePhone(agentAdjustUses.get(0).getAgentAdjustPhone());
					adjustFlowUseDtl4.setUseName(agentAdjustUses.get(0).getAgentAdjustCAccount());
					agentAdjustFlowUseDtlDao.update(adjustFlowUseDtl4);

					/***
					 * 根据选择的经理级别获取手机号码
					 */

					String messager1 = "提示：您的渠道管理系统有一张待审批工单，工单名称为'"+adjustFlowUseDtl.getFlowName()+"'";
					logger.info(messager1);
					String smsCode = "************";
					SPrivData sPrivData1 = new SPrivData();
					sPrivData1.setOpId(999990131L);
					sPrivData1.setOrgId(0L);
					channelNotifyService.sendSMSMessage(smsCode, String.valueOf(agentAdjustUses.get(0).getAgentAdjustPhone()), messager1, DateUtil.getCurrDate(), sPrivData1);
				}else{
					messager = "error1";
					return messager;
				}
			}
			//完成任务
			taskService.complete(tasks.get(0).getId());
			messager = "success";
		} catch (Exception e) {
			logger.error("流程审批出现异常",e);
			messager = "error";
		}
		return messager;
	}

	@Override
	public PageData<AgentAdjustFlowUseDtl> pageAgentAdjustFlowYBDtl(
			Long flowJudgeType , String userName, PageParameter page) throws Exception {
		List<AgentAdjustFlowUseDtl> agentAdjustFlowUseDtls = new ArrayList<AgentAdjustFlowUseDtl>();
		try {
			agentAdjustFlowUseDtls = agentAdjustFlowUseDtlDao.queryAgentAdjustFlowUseDtl(flowJudgeType,userName);
		} catch (Exception e) {
			logger.error("查询流程数据失败",e);
		}
		return new PageData<AgentAdjustFlowUseDtl>(agentAdjustFlowUseDtls, page);
	}

	@Override
	public PageData<AgentAdjustFlowUseDtl> pageErrorAgentAdjustFlowUseDtl(
			Long flowType,String useName, PageParameter page)
			throws Exception {
		List<AgentAdjustFlowUseDtl> agentAdjustFlowUseDtls = new ArrayList<AgentAdjustFlowUseDtl>();
		try {
			agentAdjustFlowUseDtls = agentAdjustFlowUseDtlDao.pageErrorAgentAdjustFlowUseDtl(flowType,useName);
		} catch (Exception e) {
			logger.error("查询流程数据失败",e);
		}
		return new PageData<AgentAdjustFlowUseDtl>(agentAdjustFlowUseDtls, page);
	}

	@Override
	public PageData<ChannelNodeScore> pageChannelNodeScore(
			Long batchId,Long flowType, String flowUseName, Long orderStatus,String useName, PageParameter page) throws Exception {
		List<ChannelNodeScore> channelNodeScores = new ArrayList<ChannelNodeScore>();
		try {
			ChannelNodeScore channelNodeScore = new ChannelNodeScore();
			channelNodeScore.setBatchId(batchId);
			channelNodeScore.setFlowJudgeType(flowType);
			if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(flowUseName)){
				channelNodeScore.setFlowUseName(flowUseName);
			}else{
				channelNodeScore.setFlowUseName(useName);
			}

			channelNodeScore.setRecStatus(1);

			channelNodeScores = channelNodeScoreDao.query(channelNodeScore);
		} catch (Exception e) {
			logger.error("查询流程数据失败",e);
		}
		return new PageData<ChannelNodeScore>(channelNodeScores, page);
	}

	@Override
	public PageData<ChannelNodeStandScore> pageChannelNodeStandScore(
			Long batchId, Integer fileType, Integer scoreApartment,Long flowType, String flowUseName, Long orderStatus,String useName, PageParameter page) throws Exception {
		List<ChannelNodeStandScore> channelNodeStandScores = new ArrayList<ChannelNodeStandScore>();
		try {
			ChannelNodeStandScore channelNodeStandScore = new ChannelNodeStandScore();
			channelNodeStandScore.setBatchId(batchId);
//			channelNodeStandScore.setFileType(fileType);
			channelNodeStandScore.setScoreApartment(scoreApartment);
			channelNodeStandScore.setFlowJudgeType(flowType);
			if (com.ailk.newchnl.util.StringUtils.isNotNullOrBlank(flowUseName)){
				channelNodeStandScore.setFlowUseName(flowUseName);
			}else{
				channelNodeStandScore.setFlowUseName(useName);
			}

			channelNodeStandScore.setRecStatus(1);

			channelNodeStandScores = channelNodeStandScoreDao.query(channelNodeStandScore);
		} catch (Exception e) {
			logger.error("查询流程数据失败",e);
		}
		return new PageData<ChannelNodeStandScore>(channelNodeStandScores, page);
	}
}
