package com.ailk.newchnl.service.schedule;

import com.ailk.newchnl.constant.ChannelConstants;
import com.ailk.newchnl.dao.SyncDateForWHDao;
import com.ailk.newchnl.entity.ChannelSysBaseType;
import com.ailk.newchnl.entity.schedule.SyncDateForWH;
import com.ailk.newchnl.util.ChannelSysBaseTypeUtil;
import com.ailk.newchnl.util.SecureFileTransferProtocol;
import com.ailk.newchnl.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPFileFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 中高端外呼推送信息
 */
@Service("SyncDateForWHService")
public class SyncDateForWHServiceImpl implements SyncDateForWHService {

    private static final Logger logger = LoggerFactory.getLogger(SyncDateForWHServiceImpl.class);

    // 服务端ip地址
    private static String ftpIp = "";
    // 用户名
    private static String ftpUserName = "";
    // 密码
    private static String ftpPassword = "";
    // 经分存放文件路径
    private static String remotePath = "";
    // 渠道存放文件的路径
    private static String url_Local = "";
    // 渠道ip
    private static String localIp = "";
    // 渠道用户名
    private static String localUserName = "";
    // 渠道密码
    private static String localPassword = "";

    @Resource
    private SyncDateForWHDao syncDateForWHDao;
    @Resource
    private SyncDateForWHService syncDateForWHService;

    // 筛选需要的文件
    public static FTPFile[] selectFile(FTPClient ftpClient, String path, final String fileNameSuffix) throws Exception {
        logger.info("筛选文件路径path：" + path);
        logger.info("筛选文件名称前缀：" + fileNameSuffix);
        return ftpClient.listFiles(path, new FTPFileFilter() {
            @Override
            public boolean accept(FTPFile arg0) {
                String name = arg0.getName();
                if (name.startsWith(fileNameSuffix)) {
                    logger.info("筛选文件名称：" + name);
                    return true;
                }
                return false;
            }
        });
    }

    // 获得配置信息
    public static void getInitInfo() throws Exception {
        List<ChannelSysBaseType> channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 119, null, null);
        // 服务端ip地址
        ftpIp = channelSysBaseTypeList.get(0).getCodeName();
        // 用户名
        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 120, null, null);
        ftpUserName = channelSysBaseTypeList.get(0).getCodeName();
        // 密码
        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 121, null, null);
        ftpPassword = channelSysBaseTypeList.get(0).getCodeName();
        // 存放文件路径
        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 300, null, null);
        remotePath = channelSysBaseTypeList.get(0).getCodeName();
        // 获取本地的文件连接 存放文件路径
        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 447, null, null);
        url_Local = channelSysBaseTypeList.get(0).getCodeName();
        // 本地ip，用户名，密码
        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 63, null, null);
        localIp = channelSysBaseTypeList.get(0).getCodeName();

        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 64, null, null);
        localUserName = channelSysBaseTypeList.get(0).getCodeName();

        channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(ChannelConstants.SERVER_PARAM, 65, null, null);
        localPassword = channelSysBaseTypeList.get(0).getCodeName();
    }

    // 下载文件到本地
    public static void downloadFileToLocal(FTPFile[] ftpFiles) throws Exception {
        // 把筛选的内容保存到本地
        String ftpPath = "";
        String homePath = "";
        String fileName = null;
        for (FTPFile ftpFile : ftpFiles) {
            fileName = ftpFile.getName();
            ftpPath = remotePath + fileName;
            homePath = url_Local + fileName;
            SecureFileTransferProtocol.download(ftpIp, ftpUserName, ftpPassword, ftpPath, homePath);
            logger.info("文件保存在渠道侧主机成功");
        }
        logger.info("ftpIp:" + ftpIp);
        logger.info("ftpUserName:" + ftpUserName);
        logger.info("ftpPassword:" + ftpPassword);
        logger.info("ftpPath:" + ftpPath);
        logger.info("homePath:" + homePath);
    }

    @Override
    public void execute() throws Exception {
        List<ChannelSysBaseType> channelSysBaseTypeList =
                ChannelSysBaseTypeUtil.getChannelSysBaseTypeList(422803, null, null, null);
        try {
            if (!channelSysBaseTypeList.isEmpty()) {
                logger.info("-------开始执行配置月中高端外呼推送信息数据-------");
                for (ChannelSysBaseType entity : channelSysBaseTypeList) {
                    String nowMonth = entity.getCodeName();
                    executeAll(nowMonth);
                }
            } else {
                logger.info("------  开始执行当前月中高端外呼推送信息数据---------");
                // 获取T-1月日期
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                Calendar cal = Calendar.getInstance();
                cal.add(Calendar.MONTH, -1);
                cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
                String nowMonth = sdf.format(cal.getTime());
                executeAll(nowMonth);
            }
        } catch (Exception e) {
            logger.error("中高端外呼推送信息数据同步失败，原因是：" + e.getMessage(), e);
        }
        logger.info("-------中高端外呼推送信息数据同步执行成功-------");
    }

    @Override
    public void executeAll(String billMonth) throws Exception {
        logger.info("获取中高端外呼推送信息数据同步的配置信息===================");
        getInitInfo();
        logger.info("开始获取经分主机上的相应文件===================");
        //检查是否已经入库
        Map condition = new HashMap();
        condition.put("billMonth", billMonth);
        Long counts = syncDateForWHDao.queryInfo(condition);
        if (counts > 0) {
            logger.info("中高端外呼推送信息数据已经同步，不需要再次同步");
            return;
        }
        // 获取远程FTP连接
        FTPClient ftpClient = SecureFileTransferProtocol.connect(ftpIp, 21, ftpUserName, ftpPassword);

        //文件前缀名称
        String fileNameSuffix = "st_mkt_waihu_busi_dtl_" + billMonth;
        // 筛选经分主机上自己需要的文件
        logger.info("开始筛选经分主机上需要的文件========================");
        FTPFile[] ftpFiles = this.selectFile(ftpClient, remotePath, fileNameSuffix);
//        FTPFile[] ftpFiles = new FTPFile[1];
        if (ftpFiles.length == 0) {
            logger.info("没有在对应主机上找到中高端外呼推送信息数据相关文件！！！===================");
            return;
        }

        logger.info("已经找到中高端外呼推送信息数据文件，开始保存到渠道主机！！！===================");
        //把筛选后的文件保存到本地
        downloadFileToLocal(ftpFiles);
        logger.info("保存中高端外呼推送信息数据文件到渠道主机成功===================");

        for (FTPFile file : ftpFiles) {
            String fileName = file.getName();
//            String fileName = "st_mkt_waihu_busi_dtl_" + billMonth;
            String pathName = url_Local + fileName;
            logger.info("本次处理的文件为：" + pathName);
            FileInputStream fileInputStream = new FileInputStream(pathName);
//            FileInputStream fileInputStream = new FileInputStream("D:/st_mkt_waihu_busi_dtl_20250831.txt");
//            Scanner scanner = new Scanner(fileInputStream, "UTF-8");
            Scanner scanner = new Scanner(fileInputStream, "GBK");

            try {
                List<String> list = new ArrayList<String>();
                String str = null;
                while (scanner.hasNextLine()) {
                    str = scanner.nextLine();
                    list.add(str);
                    // 每1000行处理一次
                    if (list.size() == 1000) {
                        dealInfo(list, billMonth);
                        list.clear();
                    }
                }
                if (!CollectionUtils.isEmpty(list)) {
                    dealInfo(list, billMonth);
                    list.clear();
                }
                // 删除远程文件
                SecureFileTransferProtocol.deleteFile(ftpIp, ftpUserName, ftpPassword, remotePath + fileName);
                logger.info("删除经分侧文件成功");
            } catch (Exception e) {
                logger.info(fileName + "=====中高端外呼推送信息数据文件入库失败", e);
            } finally {
                if (fileInputStream != null) {
                    try {
                        fileInputStream.close();
                    } catch (IOException e) {
                        logger.error("IO异常", e);
                    }
                }
                if (scanner != null) {
                    scanner.close();
                }
            }
        }
        ftpClient.disconnect();
    }

    @Transactional(rollbackFor = {Exception.class}, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void toBase(List<SyncDateForWH> syncDateForWHList, String billMonth) throws Exception {
        for (SyncDateForWH entity : syncDateForWHList) {
            try {
                if (entity != null) {
                    syncDateForWHDao.insert(entity);
                }
            } catch (Exception e) {
                logger.error("入库异常", e);
            }
        }
        logger.info("中高端外呼推送信息数据文件入库成功===================");
    }

    // 处理文件内容
    public void dealInfo(List<String> list, String billMonth) throws Exception {
        List<SyncDateForWH> syncDateForWHList = new ArrayList<SyncDateForWH>();
        // 遍历每行数据
        for (int i = 0; i < list.size(); i++) {
            try {
                // 分割当前行数据（使用逗号分隔，保留空值）
                String[] parts = list.get(i).split("\\,", -1);

                if (parts.length < 4) {
                    logger.error("第" + (i + 1) + "行的数据格式不正确，字段数量不足！");
                    continue;
                }

                SyncDateForWH syncDateForWH = new SyncDateForWH();

                // 账期月份
                syncDateForWH.setBillMonth(billMonth);

                // 手机号码
                if (StringUtils.isNotNullOrBlank(parts[0])) {
                    syncDateForWH.setPhoneNo(parts[0].trim());
                }

                // 业务大类
                if (StringUtils.isNotNullOrBlank(parts[1])) {
                    syncDateForWH.setBusinessType(parts[1].trim());
                }

                // 策划名称
                if (StringUtils.isNotNullOrBlank(parts[2])) {
                    syncDateForWH.setOfferName(parts[2].trim());
                }

                // 工号
                if (StringUtils.isNotNullOrBlank(parts[3])) {
                    syncDateForWH.setJobNumber(parts[3].trim());
                }

                syncDateForWHList.add(syncDateForWH);
            } catch (Exception e) {
                logger.error("第" + (i + 1) + "行的数据异常，文件解析出错！！！", e);
                continue;
            }
        }
        syncDateForWHService.toBase(syncDateForWHList, billMonth);
        syncDateForWHList.clear();
    }
}
