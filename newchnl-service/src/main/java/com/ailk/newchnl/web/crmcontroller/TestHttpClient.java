/**
 * $Id: TestHttpClient.java,v 1.0 2016/4/11 17:22 zhangrp Exp $
 * <p/>
 * Copyright 2016 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.web.crmcontroller;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpMethod;
import org.apache.commons.httpclient.SimpleHttpConnectionManager;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpClientParams;

import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @version $Id: TestHttpClient.java,v 1.1 2016/4/11 17:22 zhangrp Exp $
 * Created on 2016/4/11 17:22
 */
public class TestHttpClient {

    public static void main(String args[]) throws Exception{
        //调用接口 新增营业厅取号配置能力数：PT-SH-FS-OI0845 接口编码
        //第二个参数：入参
        String param="{\"orgId\":\"401709\"}";
        param=URLEncoder.encode(param,"UTF-8");
        System.out.println(doPost("PT-SH-FS-OI0845", param));
    }


    public static String doPost(String busiCode, String param) throws Exception {
        HttpMethod postMethod = new PostMethod();
        //http://10.196.58.2:18085/newchnl-service/remoting/newBusimgntToCrm?busiCode=PT-SH-FS-OI0845&param={"orgId":"401709"}
        postMethod.setPath("http://127.0.0.1:8080/newchnl-service/remoting/newBusimgntToCrm");// 开发设置服务的url
        //postMethod.setPath("http://10.196.58.2:18085/newchnl-service/remoting/newBusimgntToCrm");// 测试设置服务的url
        //postMethod.setPath("http://10.10.108.224/newchnl-service-crm/remoting/newBusimgntToCrm");// 生产设置服务的url
        HttpClient client = new HttpClient(new HttpClientParams(),new SimpleHttpConnectionManager(true));
        ((PostMethod) postMethod).addParameter("busiCode", busiCode);
        ((PostMethod) postMethod).addParameter("param", param);
        client.getParams().setSoTimeout(100000);
        client.executeMethod(postMethod);
        String reStr = postMethod.getResponseBodyAsString();
        return reStr;
    }
}
