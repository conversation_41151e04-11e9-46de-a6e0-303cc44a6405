<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ailk.newchnl.dao.BusiThirdXXAssessDao">
    <select id="getSequence" resultType="long">
        select BUSI_THIRD_XX_ASSESS_SEQ.nextval from dual
    </select>

    <insert id="insert" parameterType="map">
        INSERT INTO BUSI_THIRD_XX_ASSESS_INFO (
            done_code,bill_month,channel_team,channel_entity_id,channel_entity_name,assesment_score,asses_tt_score,
            negative_penalty_score,org_id,org_name,op_id,done_date,rec_status,agent_adjust_use_id,file_name,
            file_path,role_name,bill_month_score,asses_grid_score,basic_cost_fee,market_adjust_fee

        )
        VALUES
        (#{doneCode,jdbcType=NUMERIC},
         #{billMonth,jdbcType=VARCHAR},
         #{channelTeam,jdbcType=VARCHAR},
         #{channelEntityId,jdbcType=NUMERIC},
         #{channelEntityName,jdbcType=VARCHAR},
         #{assesmentScore,jdbcType=NUMERIC},
         #{assesTTScore,jdbcType=NUMERIC},
         #{negativePenaltyScore,jdbcType=NUMERIC},
         #{orgId,jdbcType=NUMERIC},
         #{orgName,jdbcType=VARCHAR},
         #{opId,jdbcType=NUMERIC},
         #{doneDate,jdbcType=TIMESTAMP},
         #{recStatus,jdbcType=NUMERIC},
         #{agentAdjustUseId,jdbcType=NUMERIC},
         #{fileName,jdbcType=VARCHAR},
         #{filePath,jdbcType=VARCHAR},
         #{roleName,jdbcType=VARCHAR},
         #{billMonthScore,jdbcType=NUMERIC},
         #{assesGridScore,jdbcType=NUMERIC},
         #{basicCostFee,jdbcType=NUMERIC},
         #{marketAdjustFee,jdbcType=NUMERIC}
        )
    </insert>


    <select id="query" parameterType="map" resultType="com.ailk.newchnl.entity.xt.BusiThirdXXAssess">
        SELECT * FROM BUSI_THIRD_XX_ASSESS_INFO WHERE rec_status != -1
        <if test="entity.recStatusList != null and entity.recStatusList != ''">
            and rec_status in
            <foreach collection="entity.recStatusList.split(',')" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <choose>
            <when test="entity.billMonth != null and entity.billMonth != ''">
                AND bill_month = #{entity.billMonth}
            </when>
        </choose>
        <if test="entity.recStatusList != null and entity.recStatusList != ''">
            <choose>
                <when test="entity.agentAdjustUseId != null">
                    And Agent_Adjust_Use_Id = #{entity.agentAdjustUseId}
                </when>
            </choose>
        </if>
        <choose>
            <when test="entity.orgId != null">
                AND org_id = #{entity.orgId}
            </when>
        </choose>
        <choose>
            <when test="entity.channelEntityName != null">
                AND channel_entity_name = #{entity.channelEntityName}
            </when>
        </choose>
    </select>


    <update id="update" parameterType="map">
        UPDATE BUSI_THIRD_XX_ASSESS_INFO
        <set>
            <if test="recStatus != null">rec_status = #{recStatus,jdbcType=NUMERIC},</if>
            <if test="agentAdjustUseId != null">agent_adjust_use_id = #{agentAdjustUseId},</if>
        </set>

        WHERE
                1=1
                <choose>
                    <when test="doneCode != null and doneCode != ''">
                        and done_code = #{doneCode}
                    </when>
                </choose>
                <choose>
                    <when test="billMonth != null">
                        and bill_month = #{billMonth}
                    </when>
                </choose>
                <choose>
                    <when test="channelEntityName != null">
                        AND channel_entity_name = #{channelEntityName}
                    </when>
                </choose>
                <choose>
                    <when test="orgId != null">
                        AND org_Id = #{orgId}
                    </when>
                </choose>

    </update>


</mapper>