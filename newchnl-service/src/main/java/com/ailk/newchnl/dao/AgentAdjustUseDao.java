/**
 * 
 */
package com.ailk.newchnl.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.ailk.newchnl.dao.impl.BaseDao;
import com.ailk.newchnl.entity.AgentAdjustUse;
import com.ailk.newchnl.entity.ComboOption;

/**
 * <AUTHOR>
 *
 */
@Repository("agentAdjustUseDao")
public interface AgentAdjustUseDao extends BaseDao<AgentAdjustUse> {
	
	public List<ComboOption> queryResType(@Param("resType") Integer resType);

	public Long getIdByUserName(@Param("userName")String userName);

	public List<ComboOption> queryResTypeByOrgId(@Param("resType") Integer resType, @Param("orgId") Long orgId);

}
