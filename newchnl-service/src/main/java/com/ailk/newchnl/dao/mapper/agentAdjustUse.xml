<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ailk.newchnl.dao.AgentAdjustUseDao">

		<select id="queryResType" parameterType="map" resultType="com.ailk.newchnl.entity.ComboOption">
	select t.agent_adjust_use_id AS VALUE, T.AGENT_ADJUST_NAME AS TEXT
	from agent_adjust_use t
	where rec_status = 1
	<choose>
		<when test="resType != null">
			AND t.agent_adjust_type = #{resType}
		</when>
	</choose>
</select>
	<select id="getIdByUserName" parameterType="java.lang.String" resultType="java.lang.Long">
			SELECT a.agent_adjust_use_id
			FROM agent_adjust_use a
			where rec_status = 1
		<choose>
			<when test="userName != null">
				AND a.agent_adjust_c_account = #{userName}
			</when>
		</choose>
	</select>
	<select id="queryResTypeByOrgId" parameterType="map" resultType="com.ailk.newchnl.entity.ComboOption">
		select t.agent_adjust_use_id AS VALUE, T.AGENT_ADJUST_NAME AS TEXT
		from agent_adjust_use t
		where rec_status = 1
		<choose>
			<when test="resType != null">
				AND t.agent_adjust_type = #{resType}
			</when>
		</choose>

		<choose>
			<when test="orgId != null ">
				and ORG_ID = #{orgId}
			</when>
		</choose>
	</select>

	<select id="query"  parameterType="map" resultType="com.ailk.newchnl.entity.AgentAdjustUse" >
	  select t.*,t.AGENT_ADJUST_TYPE agentAdjustType1 from agent_adjust_use t where REC_STATUS = 1 
	    <choose>
		   	  <when test="entity.orgId != null ">
		   	  	and ORG_ID = #{entity.orgId}
		   	  </when>
		</choose>
		 <choose>
		   	  <when test="entity.agentAdjustUseId != null ">
		   	  	and AGENT_ADJUST_USE_ID = #{entity.agentAdjustUseId}
		   	  </when>
		  </choose>
	    <choose>
		   	  <when test="entity.agentAdjustType != null ">
		   	  	and AGENT_ADJUST_TYPE = #{entity.agentAdjustType}
		   	  </when>
		</choose>
	  	<choose>
			<when test="entity.agentAdjustName != null">
				and AGENT_ADJUST_NAME = #{entity.agentAdjustName}
			</when>
		</choose>

	</select>
	
   <insert id="insert">
		insert into agent_adjust_use
						 (AGENT_ADJUST_USE_ID,
						  AGENT_ADJUST_TYPE,
						  AGENT_ADJUST_C_ACCOUNT,
						  AGENT_ADJUST_NAME,
						  AGENT_ADJUST_PHONE,
						  CREATE_DATE,
						  UPDATE_DATE,
						  ORG_ID,
						  OP_ID,
						  ORG_NAME,
						  REC_STATUS,
						  EXT1,
						  EXT2)
					values
					  (#{agentAdjustUseId,jdbcType=NUMERIC},
					   #{agentAdjustType,jdbcType=NUMERIC},
					   #{agentAdjustCAccount,jdbcType=VARCHAR},
					   #{agentAdjustName,jdbcType=VARCHAR},
					   #{agentAdjustPhone,jdbcType=NUMERIC},
					   #{createDate,jdbcType=TIMESTAMP},
					   #{updateDate,jdbcType=TIMESTAMP},
					   #{orgId,jdbcType=NUMERIC},
					   #{opId,jdbcType=NUMERIC},
					   #{orgName,jdbcType=VARCHAR},
					   #{recStatus,jdbcType=NUMERIC},
					   #{ext1,jdbcType=NUMERIC},
					   #{ext2,jdbcType=VARCHAR})
	</insert>
	
	<update id="update">
		UPDATE agent_adjust_use
		<set>
			<if test="agentAdjustType != null">AGENT_ADJUST_TYPE = #{agentAdjustType},</if>
			<if test="agentAdjustCAccount != null  and  agentAdjustCAccount !=''">AGENT_ADJUST_C_ACCOUNT = #{agentAdjustCAccount},</if>
			<if test="agentAdjustName != null  and  agentAdjustName !=''">AGENT_ADJUST_NAME = #{agentAdjustName},</if>
			<if test="agentAdjustPhone != null">AGENT_ADJUST_PHONE = #{agentAdjustPhone},</if>
			<if test="createDate != null">CREATE_DATE = #{createDate},</if>
			<if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
			<if test="orgId != null">ORG_ID = #{orgId},</if>
			<if test="opId != null">OP_ID = #{opId},</if>
			<if test="orgName != null  and  orgName !=''">ORG_NAME = #{orgName},</if>
			<if test="recStatus != null">REC_STATUS = #{recStatus},</if>
		</set>
         WHERE   AGENT_ADJUST_USE_ID  =  #{agentAdjustUseId} 
                 and AGENT_ADJUST_TYPE = #{agentAdjustType}
                 and REC_STATUS  =  1
	</update>
	
	<select id="getSequence" resultType="long">
		SELECT SEQ_AGENT_ADJUST_USE_ID.NEXTVAL FROM DUAL
	</select>
	
</mapper>