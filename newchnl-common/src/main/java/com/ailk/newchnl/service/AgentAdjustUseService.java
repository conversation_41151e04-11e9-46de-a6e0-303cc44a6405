package com.ailk.newchnl.service;

import java.util.List;
import java.util.Map;

import com.ailk.newchnl.entity.*;
import com.ailk.newchnl.entity.flow.AgentAdjustFeeTemDtl;
import com.ailk.newchnl.entity.flow.AgentAdjustFlowUseDtl;
import com.ailk.newchnl.mybatis.pagination.PageData;
import com.ailk.newchnl.mybatis.pagination.PageParameter;
import scala.Int;

public interface AgentAdjustUseService {
	
	public String operatorAgentAdjustUse(Integer opeatorType,AgentAdjustUse agentAdjustUse) throws Exception;
	
	public PageData<AgentAdjustUse> pageAgentAdjustUse(AgentAdjustUse adjustUse,PageParameter page) throws Exception;

    public List<AgentAdjustUse> queryAgentAdjustUse(AgentAdjustUse adjustUse) throws Exception;

    
	
	public Map<String,String> saveAgentAdjustUse(AgentAdjustUse adjustUse, AgentAdjustFeeTemDtl agentAdjustFeeTemDtl) throws Exception;
	
	  /**
     * @return return
     */
    public OptionJson<ComboOption> getResCodeCombo(Integer resType);

    /**
     * @return return
     */
    public OptionJson<ComboOption> getResCodeComboByOrgId(Integer resType, SPrivData sPrivData) throws Exception;
    
    /**
     * 查询流程数据
     */
    public PageData<AgentAdjustFlowUseDtl> pageAgentAdjustFlowUseDtl(Long flowType,String flowUseName, Long orderStatus,String useName,PageParameter page) throws Exception;
    
    
    /**
     * 查询驳回流程数据
     */
    public PageData<AgentAdjustFlowUseDtl> pageErrorAgentAdjustFlowUseDtl(Long flowType,String useName,PageParameter page) throws Exception;
    
    /**
     * 查询流程数据
     */
    public PageData<AgentAdjustFlowUseDtl> pageAgentAdjustFlowYBDtl(Long flowJudgeType, String userName,PageParameter page) throws Exception;
    
    public PageData<AgentAdjustFeeTemDtl> pageAgentAdjustFeeTemDtl(Long batchId ,PageParameter page) throws Exception;
    
    /**
     * 内部审批
     * @param adjustFlowUseDtl
     * @param operaType
     * @return
     * @throws Exception
     */
    public String interiorbuildJudege(AgentAdjustFlowUseDtl adjustFlowUseDtl,Integer operaType) throws Exception;

    public PageData<ChannelNodeScore> pageChannelNodeScore(Long batchId,Long flowType, String flowUseName, Long orderStatus, String useName, PageParameter page) throws Exception;

    public PageData<ChannelNodeStandScore> pageChannelNodeStandScore(Long batchId, Integer fileType, Integer scoreApartment, Long flowType, String flowUseName, Long orderStatus, String useName, PageParameter page) throws Exception;

}
