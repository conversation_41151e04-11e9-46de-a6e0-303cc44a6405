/*
* 中高端保有项目人员月度考核得分管理
* */
define(['common/organizationTree', 'common/util','common/accMath'], function (require) {
    var accMath = require('common/accMath');
    var Util = require('common/util');

    function midHighEndRetentionAssessmentMange(config) {
        midHighEndRetentionAssessmentMange.superclass.constructor.call(this, config);
        this.init();
    }

    midHighEndRetentionAssessmentMange.ATTRS = {
        OrganizeTree: {},
        channelEntityType: {},
        districtId: {}
    };
    BUI.extend(midHighEndRetentionAssessmentMange, BUI.Base);
    BUI.augment(midHighEndRetentionAssessmentMange, {
        init: function () {

            // layout
            var myLayout = new dhtmlXLayoutObject({
                parent: document.body,
                pattern: "1C",
                offsets: {
                    top: 5,
                    right: 5,
                    bottom: 5,
                    left: 5
                },
                cells: [{
                    id: "a",
                    header: false
                }]
            });


            // Form
            var formStructure = [{
                type: "settings",
                position: "label-left",
                labelAlign: "right"
            }, {
                type: "fieldset",
                label: "中高端保有项目人员月度考核得分查询",
                offsetLeft: 20,
                offsetTop: 20,
                width: _width - 360,
                list: [{
                    type: "block",
                    name: "row3"
                }, {
                    type: "block",
                    offsetTop: 2,
                    offsetLeft: 68,
                    list: [{type: "button", name: "queryBtn", width: 30, offsetTop: 2, offsetLeft: 72, value: "查询"},
                        {type: "newcolumn", offset: 1},
                        {type: "button", name: "resetBtn", width: 30, offsetTop: 2, value: "重置"}]
                }]
            },{
                type: "fieldset",
                label: "中高端保有项目人员月度考核得分",
                offsetLeft: 20,
                offsetTop: 20,
                width: _width - 360,
                list: [{
                    type: "block",
                    name: "toolbarRow"
                }, {
                    type: "block",
                    name: "myGridRow"
                }, {
                    type: "block",
                    name: "pagingAreaRow"
                }]
            }];
            var tableWidth = document.documentElement.clientWidth * 4 / 5;
            var myForm = myLayout.cells("a").attachForm(formStructure);
            this.set("myForm", myForm);
            this.set("myLayout", myLayout);
            myForm.enableLiveValidation(true);

            myForm.addItem("row3", {
                type: "calendar",
                name: 'billMonth',
                dateFormat: "%Y%m",
                label: '月份:',
                width: 120,
                labelWidth: 160,
                labelAlign: "right"
            }, 0);

            /*myForm.addItem("toolbarRow", {
                type: "button",
                name: "editBtn",
                width: 30,
                offsetTop: 2,
                offsetLeft: 11,
                value: "修改"
            });
            myForm.addItem("toolbarRow", {type: "newcolumn", offset: 10});*/
            myForm.addItem("toolbarRow", {
                type: "button",
                name: "exportBtn",
                width: 30,
                offsetTop: 2,
                offsetLeft: 11,
                value: "导出"
            });

            myForm.addItem("toolbarRow", {type: "newcolumn", offset: 10});
            myForm.addItem("toolbarRow", {
                type: "button",
                name: "downLoadBtn",
                width: 50,
                offsetTop: 2,
                offsetLeft: 11,
                value: "附件下载"
            });

            myForm.addItem("myGridRow", {
                type: "container",
                offsetLeft: 10,
                offsetTop: 10,
                name: "mygrid",
                inputHeight: _height - 260,
                inputWidth: tableWidth - 360
            });
            myForm.addItem("pagingAreaRow", {
                type: "container",
                name: "pagingArea",
                label: "",
                inputWidth: tableWidth - 360,
                inputHeight: 30
            });

            var mygrid = new dhtmlXGridObject(myForm.getContainer("mygrid"));
            this.set("mygrid", mygrid);
            mygrid.setHeader("序列号,属地分公司,月份,角色,EBC工号名称,当月考核系数,基本费用调整费,调整费,审批状态,审批人,附件名称,附件路径,操作时间");
            mygrid.setColumnIds("doneCode,orgName,billMonth,roleName,ebcJobNumber,assessmentCoefficient,basicCostAdjustFee,adjustFee,recStatusApprove,agentAdjustUseName,fileName,filePath,doneDate");
            mygrid.setInitWidths("120,120,120,100,120,100,100,100,100,100,150,150,120");
            mygrid.setExportable(mygrid.getColumnId(0), false);
            mygrid.enableAutoHeight(false);
            mygrid.setColTypes("ro,ro,ro,ro,ro,ro,ro,ro,ro,ro,ro,ro");
             mygrid.setColumnHidden(0, true);
            // mygrid.setColumnHidden(1, true);
            // mygrid.setColumnHidden(2, true);
            mygrid.setColAlign("left,left,left,left,left,left,left,left,left,left,left,left");
            mygrid.enablePaging(true, 10, null, myForm.getContainer("pagingArea"), true);
            mygrid.init();
            mygrid.setPagingSkin("bricks");

            var _util = new Util().dhtmlxProcessOn("正在加载。。。");
            $.post(BUI.ctx + '/agentAward/getOrganizationId', function (data) {
                new Util().dhtmlxProcessOff(_util);
                if (data.orgId == 6){
                    myForm.disableItem("editBtn");
                }

            });

            this.initDomEvent();
        },
        initDomEvent: function () {
            var _this = this;
            var myForm = _this.get("myForm");
            var myGrid = _this.get("mygrid");

            myForm.attachEvent("onButtonClick", function (name) {
                if(name == 'queryBtn') {
                    _this.queryForm();
                } else if(name == 'resetBtn') {
                    location.href = location.href;
                } else if (name == 'exportBtn') {

                    var billMonth = myForm.getCalendar("billMonth").getFormatedDate("%Y%m")
                    var param = {};
                    param.billMonth = billMonth;
                    param.recStatusList = null;

                    var url = "/agentAward/midHighEndRetentionAssessmentInfoQuery";
                    var fileName = "中高端保有项目人员月度考核得分结果导出";
                    var formatData = "{}";
                    myGrid.exportData(url, param, fileName, formatData);
                }  else if (name == 'downLoadBtn') {
                    var fileName = "";
                    var filePath = "";
                    //获取行
                    var rowId = myGrid.getSelectedRowId();
                    //下面是将值进行循环
                    var selectedData = {};  //定义集合
                    var columns = myGrid.columnIds;
                    if (rowId == "" || rowId == null) {
                        dhtmlx.alert({title: "警告", text: "请选择一条信息进行附件下载！", type: "alert-error"});
                        return;
                    }
                    if (rowId.length > 1){
                        dhtmlx.alert({title: "警告", text: "不允许选择多条信息进行附件下载！", type: "alert-error"});
                        return;
                    }
                    for (var i = 0; i < columns.length; i++) {
                        selectedData[columns[i]] = myGrid.cells(rowId, i).getValue();
                    }
                    if (selectedData.fileName != "" || selectedData.fileName != null) {
                        fileName = selectedData.fileName;
                        filePath = selectedData.filePath;
                    } else {
                        dhtmlx.alert({title: "警告", text: "该条数据没有附件信息，请联系管理员进行处理！", type: "alert-error"});
                        return;
                    }

                    var util = new Util();
                    util.download(filePath,fileName, true)
                    return;
                } else if (name == 'editBtn') {
                    //获取行
                    var rowId = myGrid.getSelectedRowId();
                    if (rowId == null) {
                        dhtmlx.alert({
                            title: "信息", text: "请选择需要修改的信息！", type: "alert-warning"
                        });
                        return;
                    }
                    //下面是将值进行循环
                    var selectedData = {};  //定义集合
                    var columns = myGrid.columnIds;
                    for (var i = 0; i < columns.length; i++) {
                        selectedData[columns[i]] = myGrid.cells(rowId, i).getValue();
                    }
                    var billMonthY=new Date().getFullYear();
                    var billMonthM=new Date().getMonth();
                    var billMonthD=new Date().getDate();
                    if (billMonthD <= 15){
                        dhtmlx.alert({
                            title: "信息", text: "只能修改自然月15日至月末的数据！", type: "alert-warning"
                        });
                        return;
                    }
                    if(billMonthM<10){
                        billMonthM += 1;
                        billMonthM="0"+billMonthM
                    }
                    if (billMonthM == 0){
                        billMonthY = billMonthY - 1;
                        billMonthM = 12;
                    }
                    var nowMonth =billMonthY+""+billMonthM;
                    if (selectedData.billMonth != nowMonth) {
                        dhtmlx.alert({
                            title: "信息", text: "只能修改当月的数据！", type: "alert-warning"
                        });
                        return;
                    }

                    var mywins = new dhtmlXWindows();
                    var mywin = mywins.createWindow({
                        id: 'window_id',
                        width: document.documentElement.clientWidth * 4 / 5,
                        height: document.documentElement.clientHeight * 4 / 5,
                        center: true,
                        park: false,
                        resize: false,
                        modal: true
                    });
                    mywin.setText("中高端保有项目人员月度考核得分信息修改");
                    mywin.keepInViewport(true);

                    var queryFormData = [{
                        type: "fieldset",
                        offsetLeft: 30,
                        label: "中高端保有项目人员月度考核得分信息修改",
                        width: document.documentElement.clientWidth * 4 / 5 - 80,
                        list: [{
                            type: "block",
                            offsetTop: 10,
                            list: [{
                                type: "hidden",
                                name: "doneCode"
                            },{
                                type: "newcolumn",
                                offset: 40
                            },{
                                type: "hidden",
                                name: "agentAdjustUseId"
                            },{
                                type: "newcolumn",
                                offset: 40
                            },{
                                type: "hidden",
                                name: "orgName"
                            },{
                                type: "newcolumn",
                                offset: 40
                            },{
                                type: "hidden",
                                name: "recStatus"
                            },{
                                type: "newcolumn",
                                offset: 40
                            },{
                                type: "input",
                                name: "billMonth",
                                label: "月份:",
                                inputWidth: 120,
                                readonly: true,
                                required: true,
                                disabled: true,
                                labelWidth: 120,
                                labelAlign: "right"
                            }, {
                                type: "newcolumn",
                                offset: 40
                            },{
                                type: "input",
                                name: "ebcJobNumber",
                                label: "EBC工号名称:",
                                inputWidth: 120,
                                required: true,
                                readonly: true,
                                disabled: true,
                                labelWidth: 160,
                                labelAlign: "right"
                            }]
                        }, {
                            type: "block",
                            offsetTop: 10,
                            list: [{
                                type: "input",
                                note:{
                                    text: '当月考核系数【0.80-1.20】'
                                },
                                name: "assessmentCoefficient",
                                label: "当月考核系数:",
                                required: true,
                                inputWidth: 120,
                                validate: "checkAssessmentCoefficient",
                                labelWidth: 160,
                                labelAlign: "right"
                            },{
                                type: "newcolumn",
                                offset: 40
                            },{
                                type: "input",
                                note:{
                                    text: '基本费用调整费'
                                },
                                name: "basicCostAdjustFee",
                                label: "基本费用调整费:",
                                required: true,
                                inputWidth: 120,
                                validate: "checkBasicCostAdjustFee",
                                labelWidth: 160,
                                labelAlign: "right"
                            }]
                        }, {
                            type: "block",
                            offsetTop: 10,
                            list: [{
                                type: "input",
                                note : {
                                    text : '调整费'
                                },
                                name: "adjustFee",
                                label: "调整费:",
                                required: true,
                                inputWidth: 120,
                                validate: "checkAdjustFee",
                                labelWidth: 160,
                                labelAlign: "right"
                            }]
                        }]
                    }, {
                        type: "block",
                        blockOffset: 220,
                        list: [{type: "button", name: "submitsWin", width: 30, value: "确定"},
                            {type: "newcolumn", offset: 70},
                            {type: "button", name: "cancelWin", width: 30, value: "取消"}]
                    }];

                    var winform = mywin.attachForm(queryFormData);
                    winform.load({data: selectedData});

                    winform.attachEvent("onChange", function (name, value) {

                    });

                    winform.attachEvent("onButtonClick", function (name) {
                        if (name == "cancelWin") {
                            mywins.unload();
                        } else if (name =="submitsWin") {
                            var selectedData = {};  //定义集合
                            var columns = myGrid.columnIds;
                            for (var i = 0; i < columns.length; i++) {
                                selectedData[columns[i]] = myGrid.cells(rowId, i).getValue();
                            }
                            var doneCode = winform.getItemValue("doneCode");
                            var billMonth = winform.getItemValue("billMonth");
                            var ebcJobNumber = winform.getItemValue("ebcJobNumber");
                            var agentAdjustUseId = winform.getItemValue("agentAdjustUseId");
                            var orgName = winform.getItemValue("orgName");
                            var recStatus = winform.getItemValue("recStatus");
                            var assessmentCoefficient = winform.getItemValue("assessmentCoefficient");
                            var basicCostAdjustFee = winform.getItemValue("basicCostAdjustFee");
                            var adjustFee = winform.getItemValue("adjustFee");
                            if (selectedData.recStatus == 0 || selectedData.recStatus == 1 || selectedData.recStatus == 2) {
                                dhtmlx.alert({
                                    title:"提示",text:"已审批的工单不允许再次修改，请至录入界面重新录入！", type:"alert-error"
                                });
                                return;
                            }

                            var regCoefficient = /^(0\.[8-9][0-9]|1\.[0-1][0-9]|1\.20)$/;
                            var regFee = /^\d+(\.\d{1,2})?$/;
                            if(!regCoefficient.test(assessmentCoefficient + "")){
                                dhtmlx.alert({
                                    title:"提示",text:"您所输入的当月考核系数不符合规范（0.80-1.20），请重新输入！", type:"alert-error"
                                });
                                return;
                            }
                            if(!regFee.test(basicCostAdjustFee + "")){
                                dhtmlx.alert({
                                    title:"提示",text:"您所输入的基本费用调整费格式不正确，请重新输入！", type:"alert-error"
                                });
                                return;
                            }
                            if(!regFee.test(adjustFee + "")){
                                dhtmlx.alert({
                                    title:"提示",text:"您所输入的调整费格式不正确，请重新输入！", type:"alert-error"
                                });
                                return;
                            }

                            dhtmlx.message({
                                type: "confirm-warning",
                                text: "您确定要修改中高端保有项目人员月度考核得分信息吗？",
                                ok: "确定",
                                cancel: "取消",
                                callback: function (a) {
                                    if (a) {
                                        var _util = new Util().dhtmlxProcessOn("正在操作。。。");
                                        var param = {
                                            doneCode: doneCode,
                                            billMonth: billMonth,
                                            ebcJobNumber: ebcJobNumber,
                                            assessmentCoefficient: assessmentCoefficient,
                                            basicCostAdjustFee: basicCostAdjustFee,
                                            adjustFee: adjustFee,
                                            agentAdjustUseId: agentAdjustUseId,
                                            recStatus: recStatus
                                        };
                                        $.post(BUI.ctx + '/agentAward/midHighEndRetentionAssessmentEdit', param, function (data) {
                                            new Util().dhtmlxProcessOff(_util);
                                            if ("1" == data.status) {
                                                dhtmlx.alert({title: "错误", text: data.message, type: "alert-error"});
                                            } else if ("0" == data.status) {
                                                dhtmlx.alert({
                                                    title: '消息', text: data.message, callback: function () {
                                                        mywins.unload();
                                                        _this.queryForm();
                                                    }
                                                });
                                            }
                                        });

                                    }
                                }
                            });
                        }
                    });
                }

            });

        }, queryForm: function () {
            var _this = this;

            var cells_a = _this.get("myLayout").cells("a");
            var myForm = _this.get("myForm");
            var myGrid = _this.get("mygrid");

            var billMonth = myForm.getCalendar("billMonth").getFormatedDate("%Y%m");
            var param = {};
            param.billMonth = billMonth;
            param.recStatusList = null;

            cells_a.progressOn();
            var url = BUI.ctx + "/agentAward/midHighEndRetentionAssessmentInfoQuery?" + $.param(param);
            myGrid.clearAndLoad(url, function () {
                cells_a.progressOff();
            }, "js");
        }
    });

    return midHighEndRetentionAssessmentMange;
});
