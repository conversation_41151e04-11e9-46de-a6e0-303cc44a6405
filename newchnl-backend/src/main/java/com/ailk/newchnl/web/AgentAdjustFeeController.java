/**
 * 
 */
package com.ailk.newchnl.web;

import com.ailk.newchnl.entity.*;
import com.ailk.newchnl.entity.flow.AgentAdjustFeeTemDtl;
import com.ailk.newchnl.entity.flow.AgentAdjustFlowUseDtl;
import com.ailk.newchnl.entity.xt.BusiThirdXXAssess;
import com.ailk.newchnl.mybatis.pagination.PageData;
import com.ailk.newchnl.mybatis.pagination.PageParameter;
import com.ailk.newchnl.service.AgentAdjustUseService;
import com.ailk.newchnl.service.AgentServiceFeeService;
import com.ailk.newchnl.service.ChannelNodeScoreService;
import com.ailk.newchnl.service.ChannelSysBaseTypeService;
import com.ailk.newchnl.util.ChannelUtil;
import com.ailk.newchnl.util.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.*;

/**
 * <AUTHOR>  都没注释的
 *
 */
@Controller
@RequestMapping("/agentAdjustFeeCller")
public class AgentAdjustFeeController {
    
	private static final Logger logger = org.slf4j.LoggerFactory.getLogger(AgentAdjustFeeController.class);
	@Resource
	private ChannelSysBaseTypeService channelSysBaseTypeService;

	@Resource
	private AgentAdjustUseService  agentAdjustUseService;

	@Resource
	private AgentServiceFeeService agentServiceFeeService;

	@Resource
	private ChannelNodeScoreService channelNodeScoreService;
	
	
	@RequestMapping(value = "/agentTemMenue")
	public String agentTemMenue(){
		return "fee/agentTemMenue";
	}
	
	
	/***
	 * 二级审批
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/errorDoneMenue")
	public String errorDoneMenue(){
		return "fee/errorDoneQuery";
	}
	
	
	/***
	 * 二级审批
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/doneMenue")
	public String doneMenue(){
		return "fee/doneQuery";
	}
	
	
	 @RequestMapping(value = "/interiorbuildJudege")
	 @ResponseBody
	 public Map<String,String> interiorbuildJudege(HttpServletRequest request,HttpServletResponse response,
			 @RequestParam(value="batchId",required=true)Long batchId,
			 @RequestParam(value="flowId",required=true)Long flowId,
			 @RequestParam(value="flowName",required=true)String flowName,
			 @RequestParam(value="notes",required=true)String notes,
			 @RequestParam(value="operateType",required=true)Integer operateType) throws Exception{
		 Map<String,String> map = new HashMap<String, String>();
		 try {
			 SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
			 if(sPrivData == null){
				map.put("status","errorSpriv");
				return map;
			 }
			flowName = java.net.URLDecoder.decode(URLDecoder.decode(flowName, "UTF-8"), "UTF-8");
			notes = java.net.URLDecoder.decode(URLDecoder.decode(notes, "UTF-8"), "UTF-8");
			AgentAdjustFlowUseDtl agentAdjustFlowUseDtl = new AgentAdjustFlowUseDtl();
			agentAdjustFlowUseDtl.setBatchId(batchId);
			agentAdjustFlowUseDtl.setFlowId(flowId);
			agentAdjustFlowUseDtl.setFlowName(flowName);
			agentAdjustFlowUseDtl.setUseName(sPrivData.getUserName());
			agentAdjustFlowUseDtl.setExt4(notes); //驳回原因
			String messager = agentAdjustUseService.interiorbuildJudege(agentAdjustFlowUseDtl, operateType);
			if(messager.equals("success")){
				map.put("status", "0");
				map.put("messager", "操作成功");
			}if(messager.equals("error")){
				map.put("status", "1");
				map.put("messager", "操作失败");
			}if(messager.equals("error1")){
				map.put("status", "3");
				map.put("messager", "此酬金合作方数据无需要处理记录，请刷新界面");
			}
		} catch (Exception e) {
			logger.error("内部审批失败",e);
			map.put("status", "2");
			map.put("messager", "操作失败" + e.getMessage());
		}
		 return map;
	 }
	 
	 //审批
	 @RequestMapping(value = "/agentAdjustJudge")
	 @ResponseBody
	 public Map<String,String> agentAdjustJudge(HttpServletRequest request,HttpServletResponse response,
			 @RequestParam(value="batchId",required=true)Long batchId,
			 @RequestParam(value="flowId",required=true)Long flowId,
			 @RequestParam(value="flowName",required=true)String flowName,
			 @RequestParam(value="flowUseName",required=true)String flowUseName,
			 @RequestParam(value="flowType",required=true)Long flowType,
			 @RequestParam(value="operateType",required=true)Integer operateType) throws Exception{
		 Map<String,String> map = new HashMap<String, String>();
		 try {
			 SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
			 if(sPrivData == null){
				map.put("status","errorSpriv");
				return map;
			 }
			flowName = java.net.URLDecoder.decode(URLDecoder.decode(flowName, "UTF-8"), "UTF-8");
			flowUseName = java.net.URLDecoder.decode(URLDecoder.decode(flowUseName, "UTF-8"), "UTF-8");
			AgentAdjustFlowUseDtl agentAdjustFlowUseDtl = new AgentAdjustFlowUseDtl();
			agentAdjustFlowUseDtl.setBatchId(batchId);
			agentAdjustFlowUseDtl.setFlowId(flowId);
			agentAdjustFlowUseDtl.setFlowName(flowName);
			agentAdjustFlowUseDtl.setFlowUseName(flowUseName);
			agentAdjustFlowUseDtl.setFlowType(flowType);
			agentAdjustFlowUseDtl.setUseName(sPrivData.getUserName());
			String messager = agentAdjustUseService.interiorbuildJudege(agentAdjustFlowUseDtl, operateType);
			if(messager.equals("success")){
				map.put("status", "0");
				map.put("messager", "操作成功");
			}if(messager.equals("error")){
				map.put("status", "1");
				map.put("messager", "操作失败");
			}if(messager.equals("error1")){
				map.put("status", "3");
				map.put("messager", "此酬金合作方数据无需要处理记录，请刷新界面");
			}if(messager.equals("error2")){
				map.put("status", "3");
				map.put("messager", "对不起，您绑定的审批人数据已经失效，请重新选择");
			}
		} catch (Exception e) {
			logger.error("内部审批失败",e);
			map.put("status", "2");
			map.put("messager", "操作失败" + e.getMessage());
		}
		 return map;
	 }
	
	@RequestMapping(value = "/pageAgentAdjustFeeTemDtl")
	@ResponseBody
	public PageData<AgentAdjustFeeTemDtl> pageAgentAdjustFeeTemDtl(HttpServletRequest request,HttpServletResponse response,
			@RequestParam(value="batchId",required=true)Long batchId,
			PageParameter page) throws Exception{
		return agentAdjustUseService.pageAgentAdjustFeeTemDtl(batchId, page);
	}
	
	/***
	 * 审批代办，查询流程数据
	 * @param request
	 * @param response
	 * @param flowType
	 * @param flowUseName
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/pageAgentAdjustFlowUseDtl")
	@ResponseBody
	public PageData<AgentAdjustFlowUseDtl> pageAgentAdjustFlowUseDtl(HttpServletRequest request,HttpServletResponse response,
			@RequestParam(value="flowJudgeType",required=true)Long flowJudgeType,
			@RequestParam(value="flowUseName",required=true)String flowUseName,
			@RequestParam(value="orderStatus",required=true)Long orderStatus,
			PageParameter page) throws Exception {
		PageData<AgentAdjustFlowUseDtl> pageData = new PageData<AgentAdjustFlowUseDtl>(null, page);
		flowUseName = java.net.URLDecoder.decode(URLDecoder.decode(flowUseName, "UTF-8"), "UTF-8");
		SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
		if(sPrivData != null ){
			pageData = agentAdjustUseService.pageAgentAdjustFlowUseDtl(flowJudgeType, flowUseName, orderStatus,sPrivData.getUserName(), page);	
		}
		return pageData;
	}
	
	/***
	 *驳回流程，查询流程数据
	 * @param request
	 * @param response
	 * @param flowType
	 * @param flowUseName
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/pageErrorAgentAdjustFlowUseDtl")
	@ResponseBody
	public PageData<AgentAdjustFlowUseDtl> pageErrorAgentAdjustFlowUseDtl(HttpServletRequest request,HttpServletResponse response,
			@RequestParam(value="flowJudgeType",required=true)Long flowJudgeType,
			PageParameter page) throws Exception {
		PageData<AgentAdjustFlowUseDtl> pageData = new PageData<AgentAdjustFlowUseDtl>(null, page);
		SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
		if(sPrivData != null ){
			pageData = agentAdjustUseService.pageErrorAgentAdjustFlowUseDtl(flowJudgeType,sPrivData.getUserName(), page);	
		}
		return pageData;
	}
	
	/***
	 * 已办查询，查询流程数据
	 * @param request
	 * @param response
	 * @param flowType
	 * @param flowUseName
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/pageAgentAdjustFlowYBDtl")
	@ResponseBody
	public PageData<AgentAdjustFlowUseDtl> pageAgentAdjustFlowYBDtl(HttpServletRequest request,HttpServletResponse response,
			@RequestParam(value="flowJudgeType",required=true)Long flowJudgeType,
			PageParameter page) throws Exception {
		PageData<AgentAdjustFlowUseDtl> pageData = new PageData<AgentAdjustFlowUseDtl>(null, page);
		SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
		if(sPrivData != null ){
			pageData = agentAdjustUseService.pageAgentAdjustFlowYBDtl(flowJudgeType, sPrivData.getUserName(), page);
		}
		return pageData;
	}
	
	/**
	 *审批待办 查询
	 * @return
	 */
	@RequestMapping(value ="/interiorbuildQuery")
	public String interiorbuildQuery() {
		return "fee/interiorbuildQuery";
	}
	/**
	 *第三方行销人员考核审批
	 * @return
	 */
	@RequestMapping(value ="/busiThirdXXAssessApprove")
	public String busiThirdXXAssessApprove() {
		return "fee/busiThirdXXAssessApprove";
	}

	/**
	 *第三方行销费用审批
	 * @return
	 */
	@RequestMapping(value ="/businessThirdAssessmentApprove")
	public String businessThirdAssessmentApprove() {
		return "fee/businessThirdAssessmentApprove";
	}
	
	@RequestMapping(value = "/deployMenue")
	public String deployMenue(HttpServletRequest request,HttpServletResponse response){
		SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
		List<ChannelSysBaseType> channelSysBaseTypes = null;
		if(sPrivData != null ){
			channelSysBaseTypes = channelSysBaseTypeService.getChannelSysBaseTypeList(50445, Integer.valueOf(sPrivData.getOrgId() + ""), null, null);	
		}
		String orgName = "";
		if(channelSysBaseTypes.size() > 0){
			orgName = channelSysBaseTypes.get(0).getCodeName();
		}
		request.setAttribute("orgName", orgName);
		request.setAttribute("orgId", sPrivData.getOrgId());
		request.setAttribute("opId", sPrivData.getOpId());
		return "fee/agentAdjustFeeMenue";
	}
	
	@RequestMapping(value = "/pageAgentAdjustUse")
	@ResponseBody
	public PageData<AgentAdjustUse> pageAgentAdjustUse(HttpServletRequest request,HttpServletResponse response,
			@RequestParam(value="orgId",required=true)Long orgId,
			PageParameter page) throws Exception{
		AgentAdjustUse adjustUse = new AgentAdjustUse();
		adjustUse.setOrgId(orgId);
		return agentAdjustUseService.pageAgentAdjustUse(adjustUse, page);
	}
	
	
	/**
	 * 下拉列表加载资
	 * 
	 * @param resType
	 * @return
	 */
	@RequestMapping("/queryResType")
	@ResponseBody
	public OptionJson<ComboOption> queryResType(
			HttpServletRequest request, HttpServletResponse response,
			@RequestParam(value = "resType", required = true) Integer resType) {
		OptionJson<ComboOption> optionJson = null;
		try {
			optionJson = agentAdjustUseService.getResCodeCombo(resType);
		} catch (Exception e) {
			logger.error(e.getMessage());
			logger.info("初始化错误！");
		}
		return optionJson;
	}

	/**
	 * 下拉列表加载资
	 *
	 * @param resType
	 * @return
	 */
	@RequestMapping("/queryResTypeByOrgId")
	@ResponseBody
	public OptionJson<ComboOption> queryResTypeByOrgId(
			HttpServletRequest request, HttpServletResponse response,
			@RequestParam(value = "resType", required = true) Integer resType) throws Exception {
		SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
		OptionJson<ComboOption> optionJson = null;
		try {
			optionJson = agentAdjustUseService.getResCodeComboByOrgId(resType,sPrivData);
		} catch (Exception e) {
			logger.info("初始化错误！");
		}
		return optionJson;
	}

	/**
	 * 下拉列表加载资
	 *
	 * @param resType
	 * @return
	 */
	@RequestMapping("/queryResTypeByOrgIdSpe")
	@ResponseBody
	public OptionJson<ComboOption> queryResTypeByOrgIdSpe(
			HttpServletRequest request, HttpServletResponse response,
			@RequestParam(value = "resType", required = true) Integer resType) throws Exception {
		SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
		OptionJson<ComboOption> optionJson = null;
		try {
			//针对只取市场部枚举
			sPrivData.setOrgId(6L);
			optionJson = agentAdjustUseService.getResCodeComboByOrgId(resType,sPrivData);
		} catch (Exception e) {
			logger.info("初始化错误！");
		}
		return optionJson;
	}

	
	@RequestMapping(value = "/operatorAgentAdjustUse")
	@ResponseBody
	public Map<String,String> operatorAgentAdjustUse(HttpServletRequest request,HttpServletResponse response,
			@RequestParam(value="agentAdjustType",required=true)Integer agentAdjustType,
			@RequestParam(value="agentAdjustCAccount",required=true)String agentAdjustCAccount,
			@RequestParam(value="agentAdjustName",required=true)String agentAdjustName,
			@RequestParam(value="agentAdjustPhone",required=true)Long agentAdjustPhone,
			@RequestParam(value="orgId",required=true)Long orgId,
			@RequestParam(value="opId",required=true)Long opId,
			@RequestParam(value="orgName",required=true)String orgName,
			@RequestParam(value="agentAdjustId",required=true)Long agentAdjustId,
			@RequestParam(value="opeatorType",required=true)Integer opeatorType
			){
		Map<String,String> map = new HashMap<String, String>();
		String messager = "";
		try {
			AgentAdjustUse agentAdjustUse = new AgentAdjustUse();
			if(agentAdjustType != null){
				agentAdjustUse.setAgentAdjustType(agentAdjustType);	
			}if(agentAdjustCAccount !=""){
				agentAdjustUse.setAgentAdjustCAccount(agentAdjustCAccount);	
			}if(agentAdjustName != ""){
				agentAdjustUse.setAgentAdjustName(agentAdjustName);	
			}if(agentAdjustPhone != null){
				agentAdjustUse.setAgentAdjustPhone(agentAdjustPhone);	
			}if(agentAdjustId != null){
				agentAdjustUse.setAgentAdjustUseId(agentAdjustId);
			}
			agentAdjustUse.setOpId(opId);
			agentAdjustUse.setOrgId(orgId);
			agentAdjustUse.setOrgName(orgName);
			messager = agentAdjustUseService.operatorAgentAdjustUse(opeatorType, agentAdjustUse);
			if(messager.equals("operError")){
				messager = "对不起，该从账号在渠道系统不存在！";
				map.put("status", "operError");
				map.put("messager", messager);
				return map;
			}if(messager.equals("operTimeOutError")){
				messager = "对不起，调用业务方法执行超时，调用超时30秒，请稍后刷新页面操作！！！";
				map.put("status", "operTimeOutError");
				map.put("messager", messager);
				return map;
			}if(messager.equals("success")){
				messager = "操作成功";
				map.put("status", "success");
				map.put("messager", messager);
				return map;
			}
		} catch (Exception e) {
			logger.error("审批人员维护管理操作失败",e);
			map.put("status", "error");
			map.put("messager", "审批人员维护管理操作失败" +e.getMessage());
		}
		return map;
	}

	@RequestMapping("/operateBusiThirdXXAssessApprove")
	@ResponseBody
	public ReturnMsg operateBusiThirdXXAssessApprove(HttpServletRequest request, HttpServletResponse response,
													 @RequestBody Map<String ,Object> params) {
		ReturnMsg returnMsg;
		try {
			SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
			if (sPrivData == null) {
				throw new Exception("登录信息不存在！");
			}
			Long doneCodes = Long.parseLong(params.get("doneCodes").toString());
			Integer newRecStatus = Integer.parseInt(params.get("newRecStatus").toString());
			Object agentAdjustUseId1 = params.get("agentAdjustUseId");
			BusiThirdXXAssess busiThirdXXAssess = new BusiThirdXXAssess();
			if (agentAdjustUseId1 != null) {
				Long agentAdjustUseId = Long.parseLong(agentAdjustUseId1.toString());
				busiThirdXXAssess.setAgentAdjustUseId(agentAdjustUseId);
			}
			busiThirdXXAssess.setDoneCode(doneCodes);
			busiThirdXXAssess.setRecStatus(newRecStatus);
			agentServiceFeeService.busiThirdXXAssessEdit(busiThirdXXAssess, sPrivData);
			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.FINISH, "信息审批成功！！");
		} catch (Exception e) {
			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.EXCEPTION, e.getMessage());
			logger.error("信息审批异常！", e);
		}
		return returnMsg;
		
	}

	/**
	 *第三方行销人员费用审批
	 * @return
	 */
	@RequestMapping(value ="/threeNodeNameApprove")
	public String threeNodeNameApprove() {
		return "fee/threeNodeNameApprove";
	}

	@RequestMapping("/operateThreeNodeNameFeeApprove")
	@ResponseBody
	public ReturnMsg operateThreeNodeNameFeeApprove(HttpServletRequest request, HttpServletResponse response,
													@RequestParam(value="faceId",required=true)Long faceId,
													@RequestParam(value="recStatus",required=true)int recStatus){
		ReturnMsg returnMsg ;
		try {
			SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
			if (sPrivData == null){
				logger.error("登录信息不存在！");
				throw new Exception("登录信息不存在！");
			}
			ThreeNodeNameFee threeNodeNameFee = new ThreeNodeNameFee();
			threeNodeNameFee.setFaceId(faceId);
			threeNodeNameFee.setRecStatus(recStatus);
			agentServiceFeeService.threeNodeNameFeeEdit(threeNodeNameFee);


			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.FINISH, "信息审批成功！！");
		} catch (Exception e) {
			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.EXCEPTION, e.getMessage());
			logger.error("信息审批异常！",e);
		}
		return returnMsg;

	}

    @RequestMapping("/operateBusinessThirdAssessmentApprove")
    @ResponseBody
    public ReturnMsg operateBusinessThirdAssessmentApprove(HttpServletRequest request, HttpServletResponse response,
                                                           @RequestBody Map<String ,Object> params){
        ReturnMsg returnMsg ;
        try {
            SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
            if (sPrivData == null){
                logger.error("登录信息不存在！");
                throw new Exception("登录信息不存在！");
            }
            List<String> doneCodes = (List)params.get("doneCodes");
            Integer auditStatus = Integer.parseInt(params.get("auditStatus").toString());
			Object agentAdjustUseId1 = params.get("agentAdjustUseId");
            //前台传的 序列号和文件名 获取 并割接 遍历进数组 再进行逐个审批
            List<Long> doneCode = new ArrayList<Long>();
            String[] doneCodes1 = doneCodes.get(0).split(",");
            for (int i = 0; i < doneCodes1.length; i++) {
                doneCode.add(Long.parseLong(doneCodes1[i]));
            }
            for (int i = 0; i < doneCode.size(); i++) {
                BusinessThirdAssessment businessThirdAssessment = new BusinessThirdAssessment();
                businessThirdAssessment.setDoneCode(doneCode.get(i));
                businessThirdAssessment.setAuditStatus(auditStatus);
				if (agentAdjustUseId1 != null) {
					Long agentAdjustUseId = Long.parseLong(agentAdjustUseId1.toString());
					businessThirdAssessment.setAgentAdjustUseId(agentAdjustUseId);
				}
                businessThirdAssessment.setRecStatus(1);
                agentServiceFeeService.businessThirdAssessmentEdit(businessThirdAssessment,sPrivData);
            }
            returnMsg = new ReturnMsg(ReturnMsg.ReturnType.FINISH, "信息审批成功！！");
        } catch (Exception e) {
            returnMsg = new ReturnMsg(ReturnMsg.ReturnType.EXCEPTION, e.getMessage());
            logger.error("信息审批异常！",e);
        }
        return returnMsg;

    }


	/**
	 *第三方行销项目支撑费考核审批
	 * @return
	 */
	@RequestMapping(value ="/threeSupportFeeApprove")
	public String threeSupportFeeApprove() {
		return "fee/threeSupportFeeApprove";
	}

	@RequestMapping("/operateThreeSupportFeeApprove")
	@ResponseBody
	public ReturnMsg operateThreeSupportFeeApprove(HttpServletRequest request, HttpServletResponse response,
													@RequestParam(value="doneCode",required=true)Long doneCode,
													@RequestParam(value="recStatus",required=true)int recStatus){
		ReturnMsg returnMsg ;
		try {
			SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
			if (sPrivData == null){
				logger.error("登录信息不存在！");
				throw new Exception("登录信息不存在！");
			}
			ThreeSupportFeeAdd threeSupportFeeAdd = new ThreeSupportFeeAdd();
			threeSupportFeeAdd.setDoneCode(doneCode);
			threeSupportFeeAdd.setRecStatus(recStatus);
			agentServiceFeeService.threeSupportFeeAddEdit(threeSupportFeeAdd);

			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.FINISH, "信息审批成功！！");
		} catch (Exception e) {
			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.EXCEPTION, e.getMessage());
			logger.error("信息审批异常！",e);
		}
		return returnMsg;

	}


	/**
	 *第三方直销角色调整审批
	 * @return
	 */
	@RequestMapping(value ="/busiThirdSalesRoleUpdApprove")
	public String busiThirdSalesRoleUpdApprove() {
		return "fee/busiThirdSalesRoleUpdApprove";
	}

	@RequestMapping(value ="/channelNodeScoreApprove")
	public String channelNodeScoreApprove() {
		return "fee/channelNodeScoreApprove";
	}
	@RequestMapping(value ="/channelNodeCompanyApprove")
	public String channelNodeCompanyApprove() {
		return "fee/channelNodeCompanyApprove";
	}
	@RequestMapping(value ="/channelNodeMarketApprove")
	public String channelNodeMarketApprove() {
		return "fee/channelNodeMarketApprove";
	}


	@RequestMapping("/operateBusiThirdSalesRoleUpdApprove")
	@ResponseBody
	public ReturnMsg operateBusiThirdSalesRoleUpdApprove(HttpServletRequest request, HttpServletResponse response,
													 @RequestBody Map<String ,Object> params){
		ReturnMsg returnMsg ;
		try {
			SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
			if (sPrivData == null){
				logger.error("登录信息不存在！");
				throw new Exception("登录信息不存在！");
			}
			Long agentAdjustUseId = null;
			Long doneCodes = Long.parseLong(params.get("doneCodes").toString());
			Integer recStatus = Integer.parseInt(params.get("recStatus").toString());
			Integer auditStatus = Integer.parseInt(params.get("auditStatus").toString());
			if (recStatus == 1){
				agentAdjustUseId = Long.parseLong(params.get("agentAdjustUseId").toString());
			}
			BusiThirdSalesRoleUpd busiThirdSalesRoleUpd = new BusiThirdSalesRoleUpd();
			busiThirdSalesRoleUpd.setDoneCode(doneCodes);
			busiThirdSalesRoleUpd.setRecStatus(recStatus);
			busiThirdSalesRoleUpd.setAuditStatus(auditStatus);
			busiThirdSalesRoleUpd.setAgentAdjustUseId(agentAdjustUseId);
			agentServiceFeeService.busiThirdSalesRoleUpdEdit(busiThirdSalesRoleUpd,sPrivData);
			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.FINISH, "信息审批成功！！");
		} catch (Exception e) {
			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.EXCEPTION, e.getMessage());
			logger.error("信息审批异常！",e);
		}
		return returnMsg;

	}

	@RequestMapping(value ="/tieTongSupportFeeAprove")
	public String tieTongSupportFeeAprove() {
		return "fee/tieTongSupportFeeAprove";
	}
	
	/**
	 * <p> 属地铁通直销项目费用调整
	 * <p> 审批通过按钮
	 */
	@RequestMapping("/operateTieTongSupportFeeApprove")
	@ResponseBody
	public ReturnMsg operateTieTongSupportFeeApprove(HttpServletRequest request, HttpServletResponse response,
												   @RequestBody Map<String ,Object> params){
		ReturnMsg returnMsg ;
		try {
			SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
			if (sPrivData == null){
				logger.error("登录信息不存在！");
				throw new Exception("登录信息不存在！");
			}
			
			TieTongSupportFeeAdd tieTongSupportFeeAdd = new TieTongSupportFeeAdd();
			
			long doneCode = Long.parseLong(params.get("doneCode").toString());
			int newRecStatus = Integer.parseInt(params.get("newRecStatus").toString()); // 审批状态 -1失效 0初始 1审批通过 2审批驳回 3二级审批通过 4二级审批驳回
			tieTongSupportFeeAdd.setDoneCode(doneCode);
			tieTongSupportFeeAdd.setRecStatus(newRecStatus);
			
			if (newRecStatus == 1 || newRecStatus == 3) {
				// 一级或者二级审批通过（从0变成1或者从1变成3），更新审批人ID
				long agentAdjustUseId = Long.parseLong(params.get("agentAdjustUseId").toString());
				tieTongSupportFeeAdd.setAgentAdjustUseId(agentAdjustUseId);
			}
			agentServiceFeeService.tieTongSupportFee_AddOrEdit(tieTongSupportFeeAdd, sPrivData);

			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.FINISH, "信息审批成功！！");
		} catch (Exception e) {
			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.EXCEPTION, e.getMessage());
			logger.error("信息审批异常！",e);
		}
		return returnMsg;

	}

	/**
	 *属地第三方行销即时激励审批
	 * @return
	 */
	@RequestMapping(value ="/dependencyBusiThirdXXJSJLApprove")
	public String dependencyBusiThirdXXJSJLApprove() {
		return "fee/dependencyBusiThirdXXJSJLApprove";
	}

	@RequestMapping("/operateDependencyBusiThirdXXJSJLApprove")
	@ResponseBody
	public ReturnMsg operateDependencyBusiThirdXXJSJLApprove(HttpServletRequest request, HttpServletResponse response,
														 @RequestBody Map<String ,Object> params){
		ReturnMsg returnMsg ;
		try {
			SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
			if (sPrivData == null){
				logger.error("登录信息不存在！");
				throw new Exception("登录信息不存在！");
			}
			Long agentAdjustUseId = null;
			Long doneCodes = Long.parseLong(params.get("doneCodes").toString());
			Integer recStatus = Integer.parseInt(params.get("recStatus").toString());
			Integer auditStatus = Integer.parseInt(params.get("auditStatus").toString());
			if (recStatus == 1){
				agentAdjustUseId = Long.parseLong(params.get("agentAdjustUseId").toString());
			}
			DependencyBusiThirdXXJSJL dependencyBusiThirdXXJSJL = new DependencyBusiThirdXXJSJL();
			dependencyBusiThirdXXJSJL.setDoneCode(doneCodes);
			dependencyBusiThirdXXJSJL.setAgentAdjustUseId(agentAdjustUseId);
			dependencyBusiThirdXXJSJL.setRecStatus(recStatus);
			dependencyBusiThirdXXJSJL.setAuditStatus(auditStatus);
			agentServiceFeeService.dependencyBusiThirdXXJSJLEdit(dependencyBusiThirdXXJSJL,sPrivData);

			/*BusiThirdSalesRoleUpd busiThirdSalesRoleUpd = new BusiThirdSalesRoleUpd();
			busiThirdSalesRoleUpd.setDoneCode(doneCodes);
			busiThirdSalesRoleUpd.setRecStatus(recStatus);
			busiThirdSalesRoleUpd.setAuditStatus(auditStatus);
			busiThirdSalesRoleUpd.setAgentAdjustUseId(agentAdjustUseId);
			agentServiceFeeService.busiThirdSalesRoleUpdEdit(busiThirdSalesRoleUpd,sPrivData);*/
			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.FINISH, "信息审批成功！！");
		} catch (Exception e) {
			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.EXCEPTION, e.getMessage());
			logger.error("信息审批异常！",e);
		}
		return returnMsg;

	}

	@RequestMapping(value = "/pageChannelNodeScoreDtl")
	@ResponseBody
	public PageData<ChannelNodeScore> pageChannelNodeScoreDtl(HttpServletRequest request,HttpServletResponse response,
																   @RequestParam(value="batchId",required=true)Long batchId,
																   PageParameter page) throws Exception{
		PageData<ChannelNodeScore> pageData = new PageData<ChannelNodeScore>(null, page);
		SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
		if (sPrivData == null){
			return pageData;
		}
		pageData = agentAdjustUseService.pageChannelNodeScore(batchId,null,null,null,null, page);
		return pageData;
	}

	@RequestMapping(value = "/pageChannelNodeScore")
	@ResponseBody
	public PageData<ChannelNodeScore> pageChannelNodeScore(HttpServletRequest request,HttpServletResponse response,
																	 @RequestParam(value="flowJudgeType",required=true)Long flowJudgeType,
																	 @RequestParam(value="flowUseName",required=true)String flowUseName,
																	 @RequestParam(value="orderStatus",required=true)Long orderStatus,
																	 PageParameter page) throws Exception {
		PageData<ChannelNodeScore> pageData = new PageData<ChannelNodeScore>(null, page);
		flowUseName = java.net.URLDecoder.decode(URLDecoder.decode(flowUseName, "UTF-8"), "UTF-8");
		SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
		if (sPrivData == null){
			return pageData;
		}
		if (StringUtils.isNotNullOrBlank(flowUseName) && flowUseName != sPrivData.getOpName()){
			return pageData;
		}
		pageData = agentAdjustUseService.pageChannelNodeScore(null, flowJudgeType, flowUseName, orderStatus,sPrivData.getOpName(), page);
		return pageData;
	}
	//审批 -- 网点打分
	@RequestMapping(value = "/channelNodeScoreOperate")
	@ResponseBody
	public Map<String,String> channelNodeScoreApprove(HttpServletRequest request,HttpServletResponse response,
											   @RequestParam(value="batchId",required=true)Long batchId,
											   @RequestParam(value="flowName",required=true)String flowName,
											   @RequestParam(value="flowUseName",required=true)String flowUseName,
											   @RequestParam(value="flowType",required=true)Long flowType,
											   @RequestParam(value="operateType",required=true)Integer operateType) throws Exception{
		Map<String,String> map = new HashMap<String, String>();
		try {
			SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
			if(sPrivData == null){
				map.put("status","errorSpriv");
				return map;
			}
			flowName = java.net.URLDecoder.decode(URLDecoder.decode(flowName, "UTF-8"), "UTF-8");
			flowUseName = java.net.URLDecoder.decode(URLDecoder.decode(flowUseName, "UTF-8"), "UTF-8");
			ChannelNodeScore channelNodeScore = new ChannelNodeScore();
			channelNodeScore.setBatchId(batchId);
			if (flowUseName != null){
				channelNodeScore.setFlowUseId(Long.parseLong(flowUseName));
			}
			channelNodeScore.setFlowType(flowType);
			channelNodeScore.setOpId(sPrivData.getOpId());
			channelNodeScore.setOrgId(sPrivData.getOrgId());
			channelNodeScore.setOpName(sPrivData.getOpName());
			channelNodeScore.setOrgName(sPrivData.getOrgName());
			channelNodeScore.setDoneDate(new Date());
			String messager = channelNodeScoreService.channelNodeScoreApprove(channelNodeScore, operateType);
			if(messager.equals("success")){
				map.put("status", "0");
				map.put("messager", "操作成功");
			}if(messager.equals("error")){
				map.put("status", "1");
				map.put("messager", "操作失败");
			}if(messager.equals("error1")){
				map.put("status", "3");
				map.put("messager", "此网点打分批次无需要处理记录，请刷新界面");
			}if(messager.equals("error2")){
				map.put("status", "3");
				map.put("messager", "对不起，您绑定的审批人数据已经失效，请重新选择");
			}
		} catch (Exception e) {
			logger.error("内部审批失败",e);
			map.put("status", "2");
			map.put("messager", "操作失败" + e.getMessage());
		}
		return map;
	}


	@RequestMapping(value = "/pageChannelNodeStandScoreDtl")
	@ResponseBody
	public PageData<ChannelNodeStandScore> pageChannelNodeStandScoreDtl(HttpServletRequest request,HttpServletResponse response,
															  @RequestParam(value="batchId",required=true)Long batchId,
															  PageParameter page) throws Exception{
		PageData<ChannelNodeStandScore> pageData = new PageData<ChannelNodeStandScore>(null, page);
		SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
		if (sPrivData == null){
			return pageData;
		}
		pageData = agentAdjustUseService.pageChannelNodeStandScore(batchId,null,null,null,null,null,null, page);
		return pageData;
	}

	@RequestMapping(value = "/pageChannelNodeStandScore")
	@ResponseBody
	public PageData<ChannelNodeStandScore> pageChannelNodeStandScore(HttpServletRequest request,HttpServletResponse response,
														   @RequestParam(value="flowJudgeType",required=true)Long flowJudgeType,
														   @RequestParam(value="flowUseName",required=true)String flowUseName,
														   @RequestParam(value="orderStatus",required=true)Long orderStatus,
//														   @RequestParam(value = "fileType",required = true)Integer fileType,
															@RequestParam(value = "scoreApartment",required = true)Integer scoreApartment,
														   PageParameter page) throws Exception {
		PageData<ChannelNodeStandScore> pageData = new PageData<ChannelNodeStandScore>(null, page);
		flowUseName = java.net.URLDecoder.decode(URLDecoder.decode(flowUseName, "UTF-8"), "UTF-8");
		SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
		if (sPrivData == null){
			return pageData;
		}
		if (StringUtils.isNotNullOrBlank(flowUseName) && flowUseName != sPrivData.getOpName()){
			return pageData;
		}
		pageData = agentAdjustUseService.pageChannelNodeStandScore(null, null,scoreApartment,flowJudgeType, flowUseName, orderStatus,sPrivData.getOpName(), page);
		return pageData;
	}

	//审批 -- 网点标准、结果打分
	@RequestMapping(value = "/channelNodeStandScoreOperate")
	@ResponseBody
	public Map<String,String> channelNodeStandScoreApprove(HttpServletRequest request,HttpServletResponse response,
													  @RequestParam(value="batchId",required=true)Long batchId,
													  @RequestParam(value="flowName",required=true)String flowName,
													  @RequestParam(value="flowUseName",required=true)String flowUseName,
													  @RequestParam(value="flowType",required=true)Long flowType,
													  @RequestParam(value="operateType",required=true)Integer operateType) throws Exception{
		Map<String,String> map = new HashMap<String, String>();
		try {
			SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
			if(sPrivData == null){
				map.put("status","errorSpriv");
				return map;
			}
			flowName = java.net.URLDecoder.decode(URLDecoder.decode(flowName, "UTF-8"), "UTF-8");
			flowUseName = java.net.URLDecoder.decode(URLDecoder.decode(flowUseName, "UTF-8"), "UTF-8");
			ChannelNodeStandScore channelNodeStandScore = new ChannelNodeStandScore();
			channelNodeStandScore.setBatchId(batchId);
			if (flowUseName != null){
				channelNodeStandScore.setFlowUseId(Long.parseLong(flowUseName));
			}
			channelNodeStandScore.setFlowType(flowType);
			channelNodeStandScore.setOpId(sPrivData.getOpId());
			channelNodeStandScore.setOrgId(sPrivData.getOrgId());
			channelNodeStandScore.setOpName(sPrivData.getOpName());
			channelNodeStandScore.setOrgName(sPrivData.getOrgName());
			channelNodeStandScore.setDoneDate(new Date());
			String messager = channelNodeScoreService.channelNodeStandScoreApprove(channelNodeStandScore, operateType);
			if(messager.equals("success")){
				map.put("status", "0");
				map.put("messager", "操作成功");
			}if(messager.equals("error")){
				map.put("status", "1");
				map.put("messager", "操作失败");
			}if(messager.equals("error1")){
				map.put("status", "3");
				map.put("messager", "此网点打分批次无需要处理记录，请刷新界面");
			}if(messager.equals("error2")){
				map.put("status", "3");
				map.put("messager", "对不起，您绑定的审批人数据已经失效，请重新选择");
			}
		} catch (Exception e) {
			logger.error("内部审批失败",e);
			map.put("status", "2");
			map.put("messager", "操作失败" + e.getMessage());
		}
		return map;
	}

	// 中高端保有项目人员月度考评审批页面路径
	@RequestMapping(value ="/midHighEndRetentionAssessmentApprove")
	public String midHighEndRetentionAssessmentApprove() {
		return "fee/midHighEndRetentionAssessmentApprove";
	}

	// 中高端保有项目人员月度考评审批
	@RequestMapping("/operatemidHighEndRetentionAssessmentApprove")
	@ResponseBody
	public ReturnMsg operatemidHighEndRetentionAssessmentApprove(HttpServletRequest request, HttpServletResponse response,
													 @RequestBody Map<String ,Object> params) {
		ReturnMsg returnMsg;
		try {
			SPrivData sPrivData = ChannelUtil.getSessionSPrivDataInfo(request);
			if (sPrivData == null) {
				throw new Exception("登录信息不存在！");
			}
			Long doneCodes = Long.parseLong(params.get("doneCodes").toString());
			Integer newRecStatus = Integer.parseInt(params.get("newRecStatus").toString());
			Object agentAdjustUseId1 = params.get("agentAdjustUseId");
			MidHighEndRetentionAssessment midHighEndRetentionAssessment = new MidHighEndRetentionAssessment();
			if (agentAdjustUseId1 != null) {
				Long agentAdjustUseId = Long.parseLong(agentAdjustUseId1.toString());
				midHighEndRetentionAssessment.setAgentAdjustUseId(String.valueOf(agentAdjustUseId));
			}
			midHighEndRetentionAssessment.setDoneCode(doneCodes);
			midHighEndRetentionAssessment.setRecStatus(String.valueOf(newRecStatus));
			agentServiceFeeService.midHighEndRetentionAssessmentEdit(midHighEndRetentionAssessment, sPrivData);
			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.FINISH, "信息审批成功！！");
		} catch (Exception e) {
			returnMsg = new ReturnMsg(ReturnMsg.ReturnType.EXCEPTION, e.getMessage());
			logger.error("信息审批异常！", e);
		}
		return returnMsg;

	}

}
