<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>log4j</groupId>
  <artifactId>log4j</artifactId>
  <packaging>jar</packaging>
  <name>Log4j</name>
  <version>1.2.14</version>
  <description>Log4j</description>
  <url>http://logging.apache.org/log4j/docs/</url>
  <issueManagement>
     <system>Bugzilla</system>
     <url>http://issues.apache.org/bugzilla</url>
  </issueManagement>
  <inceptionYear>1999</inceptionYear>
  <mailingLists>
	<mailingList>
		<name>log4j-user</name>
		<subscribe><EMAIL></subscribe>
		<unsubscribe><EMAIL></unsubscribe>
		<post><EMAIL></post>
		<archive>http://mail-archives.apache.org/mod_mbox/logging-log4j-dev/</archive>
		<otherArchives>
			<otherArchive>http://marc.theaimsgroup.com/?l=log4j-user&amp;r=1&amp;w=2</otherArchive>
		    <otherArchive>http://dir.gmane.org/gmane.comp.jakarta.log4j.user</otherArchive>
		</otherArchives>
	</mailingList>
	<mailingList>
		<name>log4j-dev</name>
		<subscribe><EMAIL></subscribe>
		<unsubscribe><EMAIL></unsubscribe>
		<post><EMAIL></post>
		<archive>http://mail-archives.apache.org/mod_mbox/logging-log4j-dev/</archive>
		<otherArchives>
		    <otherArchive>http://marc.theaimsgroup.com/?l=log4j-dev&amp;r=1&amp;w=2</otherArchive>
		    <otherArchive>http://dir.gmane.org/gmane.comp.jakarta.log4j.devel</otherArchive>
		</otherArchives>
	</mailingList>
  </mailingLists>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
	<connection>scm:svn:http://svn.apache.org/repos/asf/logging/log4j/tags/v1_2_14</connection>
	<developerConnection>scm:svn:https://svn.apache.org/repos/asf/logging/log4j/tags/v1_2_14</developerConnection>
    <url>http://svn.apache.org/viewcvs.cgi/logging/log4j/tags/v1_2_14/</url>
  </scm>
  <organization>
    <name>Apache Software Foundation</name>
    <url>http://www.apache.org</url>
  </organization>
</project>
