<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd ">
    <modelVersion>4.0.0</modelVersion>
    <groupId>junit</groupId>
    <artifactId>junit</artifactId>
    <version>4.10</version>
    <name>JUnit</name>
    <url>http://junit.org</url>
    <description>
        JUnit is a regression testing framework written by <PERSON> and <PERSON>.
        It is used by the developer who implements unit tests in Java.
    </description>
    <organization>
        <name>JUnit</name>
        <url>http://www.junit.org</url>
    </organization>
    <mailingLists>
        <mailingList>
            <name>JUnit Mailing List</name>
            <post><EMAIL></post>
            <archive>
                http://tech.groups.yahoo.com/group/junit/
            </archive>
        </mailingList>
    </mailingLists>
    <licenses>
        <license>
            <name>Common Public License Version 1.0</name>
            <url>http://www.opensource.org/licenses/cpl1.0.txt</url>
        </license>
    </licenses>
    <scm>
        <connection>scm:git:git://github.com/KentBeck/junit.git</connection>
        <developerConnection>scm:git:**************:KentBeck/junit.git</developerConnection>
        <url>http://github.com/KentBeck/junit/tree/master</url>
    </scm>
    <developers>
      <developer>
        <id>dsaff</id>
        <name>David Saff</name>
        <email><EMAIL></email>
      </developer>
    </developers>
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <encoding>ISO-8859-1</encoding>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
          <groupId>org.hamcrest</groupId>
          <artifactId>hamcrest-core</artifactId>
          <version>1.1</version>
          <scope>compile</scope>
        </dependency>
    </dependencies>

    <properties>
         <jdk.version>1.5</jdk.version>
    </properties>
</project>